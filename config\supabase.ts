import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Variáveis de ambiente do Supabase não configuradas');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false
  },
  db: {
    schema: 'public'
  }
});

// Configurações específicas para o projeto
export const SUPABASE_CONFIG = {
  tables: {
    conversations: 'conversations',
    messages: 'messages',
    documents: 'documents',
    document_chunks: 'document_chunks',
    interaction_logs: 'interaction_logs'
  },
  storage: {
    documents: 'documents'
  }
} as const;