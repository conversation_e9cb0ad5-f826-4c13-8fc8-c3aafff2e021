import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Verificar se as variáveis estão configuradas
export const isSupabaseConfigured = !!(supabaseUrl && supabaseAnonKey);

// Criar cliente apenas se configurado, senão usar null
export const supabase = isSupabaseConfigured
  ? createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: false
      },
      db: {
        schema: 'public'
      }
    })
  : null;

// Configurações específicas para o projeto
export const SUPABASE_CONFIG = {
  tables: {
    conversations: 'conversations',
    messages: 'messages',
    documents: 'documents',
    document_chunks: 'document_chunks',
    interaction_logs: 'interaction_logs'
  },
  storage: {
    documents: 'documents'
  }
} as const;