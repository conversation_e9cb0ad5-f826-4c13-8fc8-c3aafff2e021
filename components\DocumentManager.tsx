import React, { useState } from 'react';
import { DocumentInput } from './DocumentInput';
import type { Document } from '../types';

interface DocumentManagerProps {
  documents: Document[];
  onDeleteDocument: (id: string) => void;
  onUpload: (file: File) => void;
}

export const DocumentManager: React.FC<DocumentManagerProps> = ({
  documents,
  onDeleteDocument,
  onUpload
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'size'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date | string | number) => {
    try {
      // Converter para Date se não for
      const dateObj = date instanceof Date ? date : new Date(date);

      // Verificar se é uma data válida
      if (isNaN(dateObj.getTime())) {
        return 'Data inválida';
      }

      return dateObj.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Erro ao formatar data:', error);
      return 'Data inválida';
    }
  };

  const getStatusIcon = (status: Document['status']) => {
    switch (status) {
      case 'uploading':
        return (
          <div className="flex items-center space-x-2 text-blue-600">
            <div className="loading-modern">
              <div className="loading-dot"></div>
              <div className="loading-dot"></div>
              <div className="loading-dot"></div>
            </div>
            <span className="text-sm">Enviando...</span>
          </div>
        );
      case 'processing':
        return (
          <div className="flex items-center space-x-2 text-orange-600">
            <div className="animate-spin">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>
            <span className="text-sm">Processando...</span>
          </div>
        );
      case 'ready':
        return (
          <div className="flex items-center space-x-2 text-green-600">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span className="text-sm font-medium">Pronto</span>
          </div>
        );
      case 'error':
        return (
          <div className="flex items-center space-x-2 text-red-600">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm">Erro</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center space-x-2 text-gray-500">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm">Desconhecido</span>
          </div>
        );
    }
  };

  const sortedDocuments = [...documents].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'date':
        try {
          const dateA = a.upload_date instanceof Date ? a.upload_date : new Date(a.upload_date);
          const dateB = b.upload_date instanceof Date ? b.upload_date : new Date(b.upload_date);
          comparison = dateA.getTime() - dateB.getTime();
        } catch (error) {
          comparison = 0;
        }
        break;
      case 'size':
        comparison = a.size - b.size;
        break;
    }

    return sortOrder === 'asc' ? comparison : -comparison;
  });

  const handleSort = (field: 'name' | 'date' | 'size') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const handleDelete = (id: string) => {
    if (showDeleteConfirm === id) {
      onDeleteDocument(id);
      setShowDeleteConfirm(null);
    } else {
      setShowDeleteConfirm(id);
    }
  };

  const getSortIcon = (field: 'name' | 'date' | 'size') => {
    if (sortBy !== field) {
      return (
        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
        </svg>
      );
    }
    
    return sortOrder === 'asc' ? (
      <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
      </svg>
    ) : (
      <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4" />
      </svg>
    );
  };

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-white/50 to-gray-50/30">
      {/* Header */}
      <div className="p-6 border-b border-white/20 bg-white/80 backdrop-blur-sm">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-800">📁 Gerenciamento de Documentos</h2>
          <div className="flex items-center space-x-2 px-3 py-1 glass-card">
            <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
            <span className="text-sm text-gray-700 font-medium">
              {documents.length} documento{documents.length !== 1 ? 's' : ''}
            </span>
          </div>
        </div>
        <DocumentInput onUpload={onUpload} />
      </div>

      {/* Lista de documentos */}
      <div className="flex-1 overflow-hidden flex flex-col">
        {documents.length === 0 ? (
          <div className="flex-1 flex items-center justify-center animate-fade-in-up">
            <div className="text-center">
              <div className="relative mb-6">
                <div className="w-20 h-20 mx-auto bg-gradient-to-r from-gray-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div className="absolute -inset-2 bg-gradient-to-r from-orange-400 to-gray-400 rounded-full blur opacity-30 animate-pulse"></div>
              </div>
              <h3 className="text-lg font-medium text-gray-700 mb-2">📄 Nenhum documento carregado</h3>
              <p className="text-gray-500">Faça upload de documentos PDF para começar a usar o sistema RAG</p>
            </div>
          </div>
        ) : (
          <>
            {/* Header da tabela */}
            <div className="px-6 py-3 bg-white/60 backdrop-blur-sm border-b border-white/20">
              <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
                <button
                  onClick={() => handleSort('name')}
                  className="col-span-5 flex items-center space-x-1 hover:text-gray-900 transition-colors duration-200"
                >
                  <span>Nome</span>
                  {getSortIcon('name')}
                </button>
                
                <div className="col-span-2 text-center">Status</div>
                
                <button
                  onClick={() => handleSort('size')}
                  className="col-span-2 flex items-center justify-center space-x-1 hover:text-gray-900 transition-colors duration-200"
                >
                  <span>Tamanho</span>
                  {getSortIcon('size')}
                </button>
                
                <button
                  onClick={() => handleSort('date')}
                  className="col-span-2 flex items-center justify-center space-x-1 hover:text-gray-900 transition-colors duration-200"
                >
                  <span>Data</span>
                  {getSortIcon('date')}
                </button>
                
                <div className="col-span-1 text-center">Ações</div>
              </div>
            </div>

            {/* Lista de documentos */}
            <div className="flex-1 overflow-y-auto">
              {sortedDocuments.map((document) => (
                <div
                  key={document.id}
                  className="px-6 py-4 border-b border-white/20 hover:bg-white/30 transition-all duration-300 backdrop-blur-sm"
                >
                  <div className="grid grid-cols-12 gap-4 items-center">
                    {/* Nome */}
                    <div className="col-span-5">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <div>
                          <p className="font-medium text-gray-800 truncate">{document.name}</p>
                          {document.chunks_count && document.chunks_count > 0 && (
                            <p className="text-xs text-gray-500">{document.chunks_count} chunks processados</p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Status */}
                    <div className="col-span-2 text-center">
                      {getStatusIcon(document.status)}
                    </div>

                    {/* Tamanho */}
                    <div className="col-span-2 text-center text-sm text-gray-600">
                      {formatFileSize(document.size)}
                    </div>

                    {/* Data */}
                    <div className="col-span-2 text-center text-sm text-gray-600">
                      {formatDate(document.upload_date)}
                    </div>

                    {/* Ações */}
                    <div className="col-span-1 text-center">
                      <button
                        onClick={() => handleDelete(document.id)}
                        className={`p-2 rounded-lg hover:bg-white/30 transition-all duration-300 ${
                          showDeleteConfirm === document.id ? 'bg-orange-100 text-orange-600' : 'text-gray-500 hover:text-gray-700'
                        }`}
                        title={showDeleteConfirm === document.id ? 'Clique novamente para confirmar' : 'Deletar documento'}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>

      {/* Footer com estatísticas */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>
            {documents.length} documento{documents.length !== 1 ? 's' : ''}
          </span>
          <span>
            {formatFileSize(documents.reduce((total, doc) => total + doc.size, 0))} total
          </span>
        </div>
      </div>
    </div>
  );
};
