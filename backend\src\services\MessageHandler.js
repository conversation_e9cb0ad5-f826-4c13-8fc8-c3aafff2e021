import { Logger } from '../utils/Logger.js';
import moment from 'moment';

export class MessageHandler {
  constructor(whatsappService, ragService) {
    this.whatsappService = whatsappService;
    this.ragService = ragService;
    this.logger = new Logger();
    
    // Cache de usuários para evitar repetir saudações
    this.userSessions = new Map();
    
    // Configurações
    this.autoReplyEnabled = process.env.AUTO_REPLY_ENABLED === 'true';
    this.welcomeMessageEnabled = process.env.WELCOME_MESSAGE_ENABLED === 'true';
    this.businessHoursOnly = process.env.BUSINESS_HOURS_ONLY === 'true';
    
    // Comandos especiais
    this.commands = {
      '/help': this.handleHelpCommand.bind(this),
      '/info': this.handleInfoCommand.bind(this),
      '/horario': this.handleScheduleCommand.bind(this),
      '/contato': this.handleContactCommand.bind(this),
      '/menu': this.handleMenuCommand.bind(this),
      '/status': this.handleStatusCommand.bind(this)
    };

    // Rate limiting por usuário
    this.userRateLimit = new Map();
    this.rateLimitWindow = 60000; // 1 minuto
    this.maxMessagesPerWindow = 10;
  }

  setupHandlers() {
    this.logger.info('🔧 Configurando handlers de mensagem...');
    
    // Configurar callback para mensagens recebidas
    this.whatsappService.setOnMessage(this.handleMessage.bind(this));
    
    // Configurar callback para quando estiver pronto
    this.whatsappService.setOnReady(this.handleReady.bind(this));
    
    // Configurar callback para desconexão
    this.whatsappService.setOnDisconnected(this.handleDisconnected.bind(this));
    
    this.logger.info('✅ Handlers de mensagem configurados');
  }

  async handleMessage(message) {
    try {
      const userPhone = message.from;
      const userName = message.notifyName || message.pushname || 'Cidadão';
      const messageText = message.body?.trim();

      this.logger.info(`📨 Processando mensagem de ${userName} (${userPhone}): ${messageText}`);

      // Verificar rate limiting
      if (this.isRateLimited(userPhone)) {
        this.logger.warn(`⚠️ Rate limit atingido para ${userPhone}`);
        return;
      }

      // Filtrar mensagens vazias ou inválidas
      if (!messageText || messageText.length === 0) {
        this.logger.debug('📭 Mensagem vazia ignorada');
        return;
      }

      // Verificar se é um comando especial
      if (messageText.startsWith('/')) {
        await this.handleCommand(message, messageText);
        return;
      }

      // Verificar horário comercial se configurado
      if (this.businessHoursOnly && !this.isBusinessHours()) {
        await this.sendOutOfHoursMessage(userPhone, userName);
        return;
      }

      // Processar mensagem normal
      await this.processNormalMessage(message, messageText, userName, userPhone);

    } catch (error) {
      this.logger.error('❌ Erro ao processar mensagem:', error);
      await this.sendErrorMessage(message.from);
    }
  }

  async processNormalMessage(message, messageText, userName, userPhone) {
    try {
      // Obter ou criar sessão do usuário
      const userSession = this.getUserSession(userPhone, userName);
      
      // Enviar indicador de digitação
      await this.whatsappService.client.startTyping(userPhone);

      // Processar mensagem com RAG
      const userInfo = {
        phone: userPhone,
        name: userName,
        sessionId: userSession.id,
        conversationId: userSession.conversationId,
        hasGreeted: userSession.hasGreeted
      };

      const ragResponse = await this.ragService.processMessage(messageText, userInfo);

      // Parar indicador de digitação
      await this.whatsappService.client.stopTyping(userPhone);

      // Enviar resposta
      await this.whatsappService.sendMessage(userPhone, ragResponse.message);

      // Atualizar sessão do usuário
      this.updateUserSession(userPhone, {
        hasGreeted: true,
        lastMessage: messageText,
        lastResponse: ragResponse.message,
        lastInteraction: new Date(),
        messageCount: userSession.messageCount + 1
      });

      this.logger.info(`✅ Resposta enviada para ${userName} (confiança: ${Math.round(ragResponse.confidence * 100)}%)`);

    } catch (error) {
      this.logger.error('❌ Erro ao processar mensagem normal:', error);
      await this.sendErrorMessage(userPhone);
    }
  }

  async handleCommand(message, command) {
    const userPhone = message.from;
    const commandName = command.split(' ')[0].toLowerCase();
    
    this.logger.info(`🔧 Executando comando: ${commandName}`);

    if (this.commands[commandName]) {
      await this.commands[commandName](message);
    } else {
      await this.whatsappService.sendMessage(userPhone, 
        `❓ Comando não reconhecido: ${commandName}\n\nDigite /help para ver os comandos disponíveis.`
      );
    }
  }

  async handleHelpCommand(message) {
    const helpMessage = `🤖 *Comandos Disponíveis*

/help - Mostra esta ajuda
/info - Informações sobre a Vereadora
/horario - Horário de atendimento
/contato - Informações de contato
/menu - Menu principal
/status - Status do sistema

*Como usar:*
• Digite sua pergunta normalmente
• Use comandos com / para funções especiais
• O sistema responde automaticamente

🏛️ *Vereadora Rafaela de Nilda*
Câmara Municipal de Parnamirim/RN`;

    await this.whatsappService.sendMessage(message.from, helpMessage);
  }

  async handleInfoCommand(message) {
    const infoMessage = `🏛️ *Vereadora Rafaela de Nilda*

*Mandato:* 2021-2024
*Município:* Parnamirim/RN
*Câmara:* Municipal de Parnamirim

*Principais Bandeiras:*
• 🏥 Saúde pública de qualidade
• 🚌 Transporte público eficiente
• 🌳 Preservação ambiental
• 👥 Direitos sociais
• 📚 Educação para todos

*Comissões:*
• Saúde e Assistência Social
• Meio Ambiente
• Direitos Humanos

📞 ${process.env.GABINETE_TELEFONE || '(84) 99999-9999'}
📧 ${process.env.GABINETE_EMAIL || '<EMAIL>'}`;

    await this.whatsappService.sendMessage(message.from, infoMessage);
  }

  async handleScheduleCommand(message) {
    const scheduleMessage = `🕐 *Horário de Atendimento*

*Gabinete da Vereadora:*
📅 Segunda a Sexta-feira
⏰ ${process.env.HORARIO_INICIO || '08:00'} às ${process.env.HORARIO_FIM || '17:00'}

*Local:*
📍 Câmara Municipal de Parnamirim
🏛️ Rua [Endereço da Câmara]
🌐 Parnamirim/RN

*Agendamentos:*
📞 ${process.env.GABINETE_TELEFONE || '(84) 99999-9999'}
📧 ${process.env.GABINETE_EMAIL || '<EMAIL>'}

💡 *Dica:* Recomendamos agendar previamente para garantir atendimento personalizado.`;

    await this.whatsappService.sendMessage(message.from, scheduleMessage);
  }

  async handleContactCommand(message) {
    const contactMessage = `📞 *Contatos do Gabinete*

*Vereadora Rafaela de Nilda*
🏛️ Câmara Municipal de Parnamirim/RN

*Telefones:*
📱 ${process.env.GABINETE_TELEFONE || '(84) 99999-9999'}
☎️ (84) 3644-8000 (Câmara)

*E-mail:*
📧 ${process.env.GABINETE_EMAIL || '<EMAIL>'}

*Redes Sociais:*
📘 Facebook: /VereadoraRafaelaDeNilda
📷 Instagram: @rafaeladenilda
🐦 Twitter: @rafaeladenilda

*Endereço:*
📍 Câmara Municipal de Parnamirim
🏛️ [Endereço completo]
🌐 Parnamirim/RN - CEP: [CEP]`;

    await this.whatsappService.sendMessage(message.from, contactMessage);
  }

  async handleMenuCommand(message) {
    const menuMessage = `📋 *Menu Principal*

*O que posso ajudar você hoje?*

1️⃣ Projetos de lei em andamento
2️⃣ Saúde pública em Parnamirim
3️⃣ Transporte público
4️⃣ Questões ambientais
5️⃣ Direitos do cidadão
6️⃣ Agendar atendimento
7️⃣ Fazer denúncia/sugestão
8️⃣ Informações sobre o mandato

*Como usar:*
• Digite o número da opção
• Ou faça sua pergunta diretamente
• Use /help para ver comandos

🤖 *Assistente Virtual 24h*
🏛️ *Vereadora Rafaela de Nilda*`;

    await this.whatsappService.sendMessage(message.from, menuMessage);
  }

  async handleStatusCommand(message) {
    const whatsappStatus = this.whatsappService.getConnectionStatus();
    const ragStatus = this.ragService.getStatus();
    
    const statusMessage = `📊 *Status do Sistema*

*WhatsApp:*
${whatsappStatus.isConnected ? '🟢 Conectado' : '🔴 Desconectado'}
📱 Sessão: ${whatsappStatus.sessionName}

*Sistema RAG:*
${ragStatus.isInitialized ? '🟢 Ativo' : '🟡 Modo Offline'}
🤖 IA: ${ragStatus.isInitialized ? 'Funcionando' : 'Respostas básicas'}

*Horário:*
${this.isBusinessHours() ? '🟢 Horário comercial' : '🟡 Fora do horário'}

*Última atualização:*
⏰ ${moment().format('DD/MM/YYYY HH:mm:ss')}

✅ Sistema operacional`;

    await this.whatsappService.sendMessage(message.from, statusMessage);
  }

  async sendOutOfHoursMessage(userPhone, userName) {
    const outOfHoursMessage = `🌙 Olá ${userName}!

Obrigado por entrar em contato com o gabinete da *Vereadora Rafaela de Nilda*.

⏰ *Fora do horário de atendimento*
Nosso horário de funcionamento é:
📅 Segunda a Sexta: ${process.env.HORARIO_INICIO || '08:00'} às ${process.env.HORARIO_FIM || '17:00'}

📝 Sua mensagem foi registrada e responderemos no próximo dia útil.

*Para urgências:*
📞 ${process.env.GABINETE_TELEFONE || '(84) 99999-9999'}

🏛️ *Vereadora Rafaela de Nilda*
Câmara Municipal de Parnamirim/RN`;

    await this.whatsappService.sendMessage(userPhone, outOfHoursMessage);
  }

  async sendErrorMessage(userPhone) {
    const errorMessage = `❌ *Ops! Algo deu errado*

Desculpe, ocorreu um erro temporário ao processar sua mensagem.

*Tente novamente em alguns instantes ou:*
📞 Ligue: ${process.env.GABINETE_TELEFONE || '(84) 99999-9999'}
📧 E-mail: ${process.env.GABINETE_EMAIL || '<EMAIL>'}

🏛️ *Vereadora Rafaela de Nilda*
Câmara Municipal de Parnamirim/RN`;

    await this.whatsappService.sendMessage(userPhone, errorMessage);
  }

  getUserSession(userPhone, userName) {
    if (!this.userSessions.has(userPhone)) {
      const session = {
        id: `whatsapp_${userPhone}_${Date.now()}`,
        phone: userPhone,
        name: userName,
        conversationId: null,
        hasGreeted: false,
        createdAt: new Date(),
        lastInteraction: new Date(),
        messageCount: 0
      };
      this.userSessions.set(userPhone, session);
    }
    
    return this.userSessions.get(userPhone);
  }

  updateUserSession(userPhone, updates) {
    if (this.userSessions.has(userPhone)) {
      const session = this.userSessions.get(userPhone);
      Object.assign(session, updates);
      this.userSessions.set(userPhone, session);
    }
  }

  isRateLimited(userPhone) {
    const now = Date.now();
    const userLimit = this.userRateLimit.get(userPhone) || { count: 0, windowStart: now };
    
    // Reset window if expired
    if (now - userLimit.windowStart > this.rateLimitWindow) {
      userLimit.count = 0;
      userLimit.windowStart = now;
    }
    
    userLimit.count++;
    this.userRateLimit.set(userPhone, userLimit);
    
    return userLimit.count > this.maxMessagesPerWindow;
  }

  isBusinessHours() {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay();
    
    const startHour = parseInt(process.env.HORARIO_INICIO?.split(':')[0]) || 8;
    const endHour = parseInt(process.env.HORARIO_FIM?.split(':')[0]) || 18;
    const workDays = process.env.DIAS_FUNCIONAMENTO?.split(',').map(d => parseInt(d)) || [1,2,3,4,5];
    
    return workDays.includes(day) && hour >= startHour && hour < endHour;
  }

  async handleReady() {
    this.logger.info('🎉 WhatsApp está pronto para receber mensagens!');
  }

  async handleDisconnected() {
    this.logger.warn('⚠️ WhatsApp foi desconectado');
    // Limpar cache de sessões
    this.userSessions.clear();
    this.userRateLimit.clear();
  }

  getStats() {
    return {
      activeSessions: this.userSessions.size,
      totalMessages: Array.from(this.userSessions.values()).reduce((sum, session) => sum + session.messageCount, 0),
      rateLimitedUsers: this.userRateLimit.size,
      autoReplyEnabled: this.autoReplyEnabled,
      businessHoursOnly: this.businessHoursOnly,
      isBusinessHours: this.isBusinessHours()
    };
  }
}
