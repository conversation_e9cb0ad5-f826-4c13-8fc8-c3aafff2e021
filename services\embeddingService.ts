import { genAI, isGeminiConfigured } from '../config/gemini';
import type { EmbeddingResult } from '../types';

class EmbeddingService {
  private model = isGeminiConfigured && genAI ? genAI.getGenerativeModel({ model: 'text-embedding-004' }) : null;

  async generateEmbedding(text: string, metadata?: Record<string, any>): Promise<EmbeddingResult> {
    try {
      console.log('🧮 Gerando embedding para texto:', text.substring(0, 100) + '...');

      // Se Gemini não estiver configurado, usar embedding simulado
      if (!isGeminiConfigured || !this.model) {
        return this.generateFallbackEmbedding(text, metadata);
      }

      const result = await this.model.embedContent(text);
      const embedding = result.embedding.values;

      console.log(`✅ Embedding gerado: ${embedding.length} dimensões`);

      return {
        embedding,
        text,
        metadata
      };

    } catch (error) {
      console.error('❌ Erro ao gerar embedding:', error);
      throw new Error(`Falha ao gerar embedding: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  async generateBatchEmbeddings(texts: string[]): Promise<EmbeddingResult[]> {
    console.log(`🧮 Gerando embeddings em lote: ${texts.length} textos`);

    const results: EmbeddingResult[] = [];

    for (let i = 0; i < texts.length; i++) {
      const text = texts[i];
      console.log(`📝 Processando ${i + 1}/${texts.length}`);

      try {
        const embedding = await this.generateEmbedding(text);
        results.push(embedding);

        // Pausa pequena para não sobrecarregar a API
        if (i < texts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }

      } catch (error) {
        console.error(`❌ Erro no texto ${i + 1}:`, error);
        // Continuar com os outros textos
      }
    }

    console.log(`✅ Embeddings gerados: ${results.length}/${texts.length}`);
    return results;
  }

  // Função para calcular similaridade entre embeddings
  calculateSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings devem ter o mesmo tamanho');
    }

    // Produto escalar
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    // Similaridade do cosseno
    const similarity = dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    return similarity;
  }

  // Função para encontrar os embeddings mais similares
  findMostSimilar(
    queryEmbedding: number[],
    candidateEmbeddings: Array<{ embedding: number[]; metadata?: any }>,
    topK: number = 5
  ): Array<{ similarity: number; metadata?: any }> {
    
    const similarities = candidateEmbeddings.map(candidate => ({
      similarity: this.calculateSimilarity(queryEmbedding, candidate.embedding),
      metadata: candidate.metadata
    }));

    // Ordenar por similaridade (maior primeiro)
    similarities.sort((a, b) => b.similarity - a.similarity);

    // Retornar os top K
    return similarities.slice(0, topK);
  }

  // Método de fallback para quando Gemini não está configurado
  private generateFallbackEmbedding(text: string, metadata?: Record<string, any>): EmbeddingResult {
    console.log('⚠️ Usando embedding simulado (Gemini não configurado)');

    // Gerar embedding baseado em hash simples do texto
    const embedding = this.textToSimpleEmbedding(text);

    return {
      embedding,
      text,
      metadata
    };
  }

  private textToSimpleEmbedding(text: string): number[] {
    // Criar um embedding de 768 dimensões baseado no texto
    const dimensions = 768;
    const embedding: number[] = new Array(dimensions);

    // Usar características do texto para gerar valores
    const textLower = text.toLowerCase();
    const textLength = text.length;
    const wordCount = text.split(/\s+/).length;

    // Gerar valores baseados em características do texto
    for (let i = 0; i < dimensions; i++) {
      let value = 0;

      // Usar diferentes características para diferentes dimensões
      if (i < textLength && i < text.length) {
        value += text.charCodeAt(i % text.length) / 255.0;
      }

      // Adicionar variação baseada na posição
      value += Math.sin(i * 0.1 + textLength * 0.01) * 0.5;

      // Adicionar variação baseada no número de palavras
      value += Math.cos(i * 0.05 + wordCount * 0.02) * 0.3;

      // Normalizar para [-1, 1]
      embedding[i] = Math.tanh(value);
    }

    return this.normalizeEmbedding(embedding);
  }

  // Função para normalizar embedding
  normalizeEmbedding(embedding: number[]): number[] {
    const norm = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => val / norm);
  }

  // Função para reduzir dimensionalidade (PCA simples)
  reduceDimensions(embeddings: number[][], targetDim: number): number[][] {
    if (targetDim >= embeddings[0].length) {
      return embeddings;
    }

    // Implementação simples - apenas pegar as primeiras dimensões
    // Em produção, usar PCA real
    return embeddings.map(embedding => embedding.slice(0, targetDim));
  }
}

export const embeddingService = new EmbeddingService();
