import React, { useState, useRef, useEffect } from 'react';
import type { Message } from '../types';

interface ChatInterfaceProps {
  messages: Message[];
  onSendMessage: (content: string) => void;
  isLoading: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement>;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  onSendMessage,
  isLoading,
  messagesEndRef
}) => {
  const [inputValue, setInputValue] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim() && !isLoading) {
      onSendMessage(inputValue.trim());
      setInputValue('');
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [inputValue]);

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-white/50 to-blue-50/30">
      {/* Área de mensagens */}
      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {messages.length === 0 ? (
          <div className="text-center py-16 animate-fade-in-up">
            <div className="relative mb-8">
              <div className="w-20 h-20 mx-auto bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div className="absolute -inset-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full blur opacity-30 animate-pulse"></div>
            </div>

            <h3 className="text-2xl font-bold text-gray-800 mb-4">
              🏛️ Bem-vindo ao Assistente da Vereadora Rafaela de Nilda!
            </h3>
            <p className="text-gray-600 max-w-lg mx-auto mb-8 leading-relaxed">
              Faça suas perguntas sobre projetos de lei, serviços públicos, ou qualquer assunto relacionado ao mandato.
              Estou aqui para ajudar com informações precisas e atualizadas!
            </p>

            {/* Sugestões de perguntas */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
              {[
                "📋 Quais projetos de lei foram apresentados?",
                "🏥 Como está a situação da saúde em Parnamirim?",
                "🚌 Qual o status do transporte público?",
                "🌳 Há projetos ambientais em andamento?"
              ].map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => setInputValue(suggestion.replace(/^[^\s]+ /, ''))}
                  className="p-4 glass-card text-left hover:scale-105 transition-all duration-300 group"
                >
                  <div className="text-sm font-medium text-gray-700 group-hover:text-gray-900">
                    {suggestion}
                  </div>
                </button>
              ))}
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`message-bubble p-4 max-w-3xl ${
                message.sender === 'user' ? 'user-message' : 'bot-message'
              }`}>
                <div className="whitespace-pre-wrap">{message.content}</div>
                
                {/* Mostrar sources se existirem */}
                {message.sources && message.sources.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <p className="text-xs text-gray-500 mb-2">Fontes consultadas:</p>
                    <div className="space-y-1">
                      {message.sources.map((source, index) => (
                        <div key={source.id} className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                          <span className="font-medium">{source.title}</span>
                          <p className="mt-1">{source.content}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Mostrar confiança se existir */}
                {message.confidence !== undefined && (
                  <div className="mt-2 flex items-center space-x-2">
                    <span className="text-xs text-gray-500">Confiança:</span>
                    <div className="flex-1 bg-gray-200 rounded-full h-1.5">
                      <div 
                        className="bg-blue-600 h-1.5 rounded-full" 
                        style={{ width: `${message.confidence * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-xs text-gray-500">
                      {Math.round(message.confidence * 100)}%
                    </span>
                  </div>
                )}
                
                <div className={`text-xs mt-2 ${
                  message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  {formatTimestamp(message.timestamp)}
                </div>
              </div>
            </div>
          ))
        )}
        
        {/* Indicador de digitação moderno */}
        {isLoading && (
          <div className="flex justify-start animate-fade-in-up">
            <div className="bot-message message-bubble p-5">
              <div className="flex items-center space-x-3">
                <div className="loading-modern">
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                </div>
                <span className="text-sm text-gray-600 font-medium">Assistente está processando...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Área de input moderna */}
      <div className="relative p-6">
        {/* Background com glass morphism */}
        <div className="absolute inset-0 bg-white/80 backdrop-blur-sm border-t border-white/20"></div>

        <form onSubmit={handleSubmit} className="relative z-10">
          <div className="flex items-end space-x-4">
            <div className="flex-1 relative">
              <textarea
                ref={textareaRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Digite sua pergunta sobre a Câmara Municipal de Parnamirim..."
                className="input-modern w-full resize-none px-6 py-4 pr-12 text-gray-800 placeholder-gray-500 focus:outline-none"
                rows={1}
                style={{ minHeight: '56px', maxHeight: '120px' }}
                disabled={isLoading}
              />

              {/* Contador de caracteres */}
              <div className="absolute bottom-2 right-4 text-xs text-gray-400">
                {inputValue.length}/1000
              </div>
            </div>
            <button
              type="submit"
              disabled={!inputValue.trim() || isLoading}
              className="btn-modern px-8 py-4 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-3 group"
            >
              {isLoading ? (
                <div className="loading-modern">
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                </div>
              ) : (
                <svg className="w-5 h-5 transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              )}
              <span className="font-semibold">Enviar</span>
            </button>
          </div>
        </form>

        {/* Dicas de uso */}
        <div className="mt-4 flex items-center justify-between text-xs">
          <div className="flex items-center space-x-4 text-gray-500">
            <div className="flex items-center space-x-1">
              <kbd className="px-2 py-1 bg-white/50 rounded border border-white/20 text-gray-600">Enter</kbd>
              <span>Enviar</span>
            </div>
            <div className="flex items-center space-x-1">
              <kbd className="px-2 py-1 bg-white/50 rounded border border-white/20 text-gray-600">Shift</kbd>
              <span>+</span>
              <kbd className="px-2 py-1 bg-white/50 rounded border border-white/20 text-gray-600">Enter</kbd>
              <span>Nova linha</span>
            </div>
          </div>

          <div className="flex items-center space-x-2 text-gray-500">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>IA Online</span>
          </div>
        </div>
      </div>
    </div>
  );
};
