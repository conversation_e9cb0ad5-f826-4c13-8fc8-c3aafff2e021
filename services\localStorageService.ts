import type { Message, Document, Conversation, DocumentChunk, InteractionLog } from '../types';

class LocalStorageService {
  private getStorageKey(key: string): string {
    return `vereadora_rag_${key}`;
  }

  private getFromStorage<T>(key: string, defaultValue: T): T {
    try {
      const item = localStorage.getItem(this.getStorageKey(key));
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error(`Erro ao ler ${key} do localStorage:`, error);
      return defaultValue;
    }
  }

  private setToStorage<T>(key: string, value: T): void {
    try {
      localStorage.setItem(this.getStorageKey(key), JSON.stringify(value));
    } catch (error) {
      console.error(`Erro ao salvar ${key} no localStorage:`, error);
    }
  }

  // Conversas
  async createConversation(title: string, messages: Message[] = []): Promise<Conversation> {
    const conversations = this.getFromStorage<Conversation[]>('conversations', []);
    const newConversation: Conversation = {
      id: Date.now().toString(),
      title,
      created_at: new Date(),
      updated_at: new Date(),
      messages: messages,
      message_count: messages.length
    };

    conversations.unshift(newConversation);
    this.setToStorage('conversations', conversations);
    return newConversation;
  }

  async getConversations(): Promise<Conversation[]> {
    const conversations = this.getFromStorage<Conversation[]>('conversations', []);
    return conversations.map(conv => ({
      ...conv,
      created_at: new Date(conv.created_at),
      updated_at: new Date(conv.updated_at)
    }));
  }

  async getConversation(id: string): Promise<Conversation | null> {
    const conversations = await this.getConversations();
    return conversations.find(conv => conv.id === id) || null;
  }

  async updateConversation(id: string, updates: Partial<Conversation>): Promise<Conversation> {
    const conversations = this.getFromStorage<Conversation[]>('conversations', []);
    const index = conversations.findIndex(conv => conv.id === id);
    
    if (index === -1) {
      throw new Error('Conversa não encontrada');
    }

    conversations[index] = {
      ...conversations[index],
      ...updates,
      updated_at: new Date()
    };

    this.setToStorage('conversations', conversations);
    return conversations[index];
  }

  async deleteConversation(id: string): Promise<void> {
    const conversations = this.getFromStorage<Conversation[]>('conversations', []);
    const filtered = conversations.filter(conv => conv.id !== id);
    this.setToStorage('conversations', filtered);
  }

  // Mensagens
  async addMessageToConversation(conversationId: string, message: Message): Promise<Message> {
    const conversations = this.getFromStorage<Conversation[]>('conversations', []);
    const conversation = conversations.find(conv => conv.id === conversationId);
    
    if (!conversation) {
      throw new Error('Conversa não encontrada');
    }

    if (!conversation.messages) {
      conversation.messages = [];
    }

    conversation.messages.push(message);
    conversation.message_count = conversation.messages.length;
    conversation.updated_at = new Date();

    this.setToStorage('conversations', conversations);
    return message;
  }

  async getMessagesFromConversation(conversationId: string): Promise<Message[]> {
    const conversation = await this.getConversation(conversationId);
    return conversation?.messages || [];
  }

  // Documentos
  async saveDocument(document: Omit<Document, 'id' | 'created_at'>): Promise<Document> {
    const documents = this.getFromStorage<Document[]>('documents', []);
    const newDocument: Document = {
      ...document,
      id: Date.now().toString(),
      created_at: new Date()
    };

    documents.unshift(newDocument);
    this.setToStorage('documents', documents);
    return newDocument;
  }

  async getDocuments(): Promise<Document[]> {
    const documents = this.getFromStorage<Document[]>('documents', []);
    return documents.map(doc => ({
      ...doc,
      created_at: new Date(doc.created_at)
    }));
  }

  async deleteDocument(id: string): Promise<void> {
    const documents = this.getFromStorage<Document[]>('documents', []);
    const filtered = documents.filter(doc => doc.id !== id);
    this.setToStorage('documents', filtered);
  }

  // Chunks de documentos
  async saveDocumentChunks(chunks: Omit<DocumentChunk, 'id'>[]): Promise<DocumentChunk[]> {
    const existingChunks = this.getFromStorage<DocumentChunk[]>('document_chunks', []);
    const newChunks: DocumentChunk[] = chunks.map(chunk => ({
      ...chunk,
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }));

    existingChunks.push(...newChunks);
    this.setToStorage('document_chunks', existingChunks);
    return newChunks;
  }

  async getDocumentChunks(documentId?: string): Promise<DocumentChunk[]> {
    const chunks = this.getFromStorage<DocumentChunk[]>('document_chunks', []);
    return documentId ? chunks.filter(chunk => chunk.document_id === documentId) : chunks;
  }

  async searchDocumentChunks(query: string, limit: number = 5): Promise<DocumentChunk[]> {
    const chunks = this.getFromStorage<DocumentChunk[]>('document_chunks', []);
    const queryLower = query.toLowerCase();
    
    // Busca simples por texto
    const matches = chunks.filter(chunk => 
      chunk.content.toLowerCase().includes(queryLower)
    );

    return matches.slice(0, limit);
  }

  // Logs de interação
  async logInteraction(log: Omit<InteractionLog, 'id' | 'timestamp'>): Promise<InteractionLog> {
    const logs = this.getFromStorage<InteractionLog[]>('interaction_logs', []);
    const newLog: InteractionLog = {
      ...log,
      id: Date.now().toString(),
      timestamp: new Date()
    };

    logs.push(newLog);
    
    // Manter apenas os últimos 1000 logs para não sobrecarregar o localStorage
    if (logs.length > 1000) {
      logs.splice(0, logs.length - 1000);
    }

    this.setToStorage('interaction_logs', logs);
    return newLog;
  }

  async getInteractionLogs(limit?: number): Promise<InteractionLog[]> {
    const logs = this.getFromStorage<InteractionLog[]>('interaction_logs', []);
    const sortedLogs = logs
      .map(log => ({ ...log, timestamp: new Date(log.timestamp) }))
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    
    return limit ? sortedLogs.slice(0, limit) : sortedLogs;
  }

  // Métricas
  async getMetrics(): Promise<{
    totalConversations: number;
    totalMessages: number;
    totalDocuments: number;
    totalInteractions: number;
  }> {
    const conversations = this.getFromStorage<Conversation[]>('conversations', []);
    const documents = this.getFromStorage<Document[]>('documents', []);
    const logs = this.getFromStorage<InteractionLog[]>('interaction_logs', []);
    
    const totalMessages = conversations.reduce((sum, conv) => sum + (conv.message_count || 0), 0);

    return {
      totalConversations: conversations.length,
      totalMessages,
      totalDocuments: documents.length,
      totalInteractions: logs.length
    };
  }

  // Limpar dados
  async clearAllData(): Promise<void> {
    const keys = ['conversations', 'documents', 'document_chunks', 'interaction_logs'];
    keys.forEach(key => {
      localStorage.removeItem(this.getStorageKey(key));
    });
  }
}

export const localStorageService = new LocalStorageService();
