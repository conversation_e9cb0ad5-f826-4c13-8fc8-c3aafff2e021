import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Importar serviços
import { WhatsAppService } from './services/WhatsAppService.js';
import { RAGService } from './services/RAGService.js';
import { MessageHandler } from './services/MessageHandler.js';
import { SessionManager } from './services/SessionManager.js';
import { PersistenceService } from './services/PersistenceService.js';
import { Logger } from './utils/Logger.js';
import { authenticateApiKey, verifyOrigin, rateLimitByIP, auditLog } from './middleware/auth.js';

// Importar rotas
import whatsappRoutes from './routes/whatsapp.js';
import sessionRoutes from './routes/session.js';
import webhookRoutes from './routes/webhook.js';
import healthRoutes from './routes/health.js';
import securityRoutes from './routes/security.js';

// Configuração de paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Carregar variáveis de ambiente
dotenv.config({ path: join(__dirname, '../.env') });

class Server {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 3001;
    this.logger = new Logger();
    
    // Inicializar serviços
    this.whatsappService = new WhatsAppService();
    this.ragService = new RAGService();
    this.sessionManager = new SessionManager();
    this.persistenceService = new PersistenceService();
    this.messageHandler = new MessageHandler(this.whatsappService, this.ragService);
    
    this.setupMiddlewares();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddlewares() {
    // Middleware de auditoria
    this.app.use(auditLog);

    // Verificação de origem (apenas para APIs sensíveis)
    this.app.use('/api/whatsapp', verifyOrigin);
    this.app.use('/api/security', verifyOrigin);

    // Segurança
    this.app.use(helmet({
      contentSecurityPolicy: false,
      crossOriginEmbedderPolicy: false
    }));

    // CORS
    this.app.use(cors({
      origin: [
        process.env.FRONTEND_URL || 'http://localhost:3000',
        'http://localhost:3000',
        'http://127.0.0.1:3000'
      ],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
    }));

    // Compressão
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 60000,
      max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
      message: {
        error: 'Muitas requisições. Tente novamente em alguns minutos.',
        code: 'RATE_LIMIT_EXCEEDED'
      },
      standardHeaders: true,
      legacyHeaders: false
    });
    this.app.use('/api/', limiter);

    // Logging
    this.app.use(morgan('combined', {
      stream: { write: (message) => this.logger.info(message.trim()) }
    }));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Servir arquivos estáticos (QR codes, etc.)
    this.app.use('/static', express.static(join(__dirname, '../public')));
  }

  setupRoutes() {
    // Rota de boas-vindas
    this.app.get('/', (req, res) => {
      res.json({
        message: '🏛️ Backend WhatsApp - Vereadora Rafaela de Nilda',
        version: '1.0.0',
        status: 'online',
        timestamp: new Date().toISOString(),
        endpoints: {
          health: '/api/health',
          whatsapp: '/api/whatsapp',
          session: '/api/session',
          webhook: '/api/webhook'
        }
      });
    });

    // Rotas da API
    this.app.use('/api/health', healthRoutes);
    this.app.use('/api/whatsapp', authenticateApiKey(['whatsapp:read']), whatsappRoutes);
    this.app.use('/api/session', authenticateApiKey(['session:read']), sessionRoutes);
    this.app.use('/api/webhook', webhookRoutes);
    this.app.use('/api/security', securityRoutes);

    // Rota 404
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Endpoint não encontrado',
        message: 'A rota solicitada não existe neste servidor',
        availableEndpoints: [
          '/api/health',
          '/api/whatsapp',
          '/api/session',
          '/api/webhook'
        ]
      });
    });
  }

  setupErrorHandling() {
    // Middleware de tratamento de erros
    this.app.use((error, req, res, next) => {
      this.logger.error('Erro no servidor:', error);

      // Erro de validação
      if (error.name === 'ValidationError') {
        return res.status(400).json({
          error: 'Dados inválidos',
          message: error.message,
          code: 'VALIDATION_ERROR'
        });
      }

      // Erro de autenticação
      if (error.name === 'UnauthorizedError') {
        return res.status(401).json({
          error: 'Não autorizado',
          message: 'Token de acesso inválido ou expirado',
          code: 'UNAUTHORIZED'
        });
      }

      // Erro genérico
      res.status(500).json({
        error: 'Erro interno do servidor',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Algo deu errado',
        code: 'INTERNAL_SERVER_ERROR'
      });
    });

    // Tratamento de promises rejeitadas
    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('Promise rejeitada não tratada:', reason);
    });

    // Tratamento de exceções não capturadas
    process.on('uncaughtException', (error) => {
      this.logger.error('Exceção não capturada:', error);
      process.exit(1);
    });
  }

  async start() {
    try {
      // Inicializar serviços
      await this.initializeServices();

      // Iniciar servidor
      this.app.listen(this.port, () => {
        this.logger.info(`🚀 Servidor iniciado na porta ${this.port}`);
        this.logger.info(`🏛️ Backend WhatsApp - Vereadora Rafaela de Nilda`);
        this.logger.info(`📱 Ambiente: ${process.env.NODE_ENV || 'development'}`);
        this.logger.info(`🌐 URL: http://localhost:${this.port}`);
      });

    } catch (error) {
      this.logger.error('Erro ao iniciar servidor:', error);
      process.exit(1);
    }
  }

  async initializeServices() {
    this.logger.info('🔧 Inicializando serviços...');

    try {
      // Disponibilizar serviços para as rotas
      this.app.locals.whatsappService = this.whatsappService;
      this.app.locals.ragService = this.ragService;
      this.app.locals.messageHandler = this.messageHandler;
      this.app.locals.sessionManager = this.sessionManager;
      this.app.locals.persistenceService = this.persistenceService;

      // Inicializar WhatsApp Service
      await this.whatsappService.initialize();
      this.logger.info('✅ WhatsApp Service inicializado');

      // Inicializar RAG Service
      await this.ragService.initialize();
      this.logger.info('✅ RAG Service inicializado');

      // Inicializar Session Manager
      await this.sessionManager.initialize();
      this.logger.info('✅ Session Manager inicializado');

      // Configurar handlers de mensagem
      this.messageHandler.setupHandlers();
      this.logger.info('✅ Message Handlers configurados');

      this.logger.info('🎉 Todos os serviços inicializados com sucesso!');

    } catch (error) {
      this.logger.error('❌ Erro ao inicializar serviços:', error);
      throw error;
    }
  }

  async stop() {
    this.logger.info('🛑 Parando servidor...');
    
    try {
      // Parar serviços
      await this.whatsappService.stop();
      await this.sessionManager.cleanup();
      
      this.logger.info('✅ Servidor parado com sucesso');
    } catch (error) {
      this.logger.error('❌ Erro ao parar servidor:', error);
    }
  }
}

// Inicializar e iniciar servidor
const server = new Server();

// Graceful shutdown
process.on('SIGTERM', async () => {
  await server.stop();
  process.exit(0);
});

process.on('SIGINT', async () => {
  await server.stop();
  process.exit(0);
});

// Iniciar servidor
server.start().catch((error) => {
  console.error('Falha ao iniciar servidor:', error);
  process.exit(1);
});

export { server as default, Server };
