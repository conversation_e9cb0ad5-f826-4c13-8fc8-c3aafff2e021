import React, { useState, useEffect } from 'react';
import { monitoringService } from '../services/monitoringService';
import type { MonitoringMetrics } from '../types';

export const MonitoringDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<MonitoringMetrics | null>(null);
  const [realTimeStats, setRealTimeStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    loadMetrics();
    loadRealTimeStats();

    // Atualizar a cada 30 segundos
    const interval = setInterval(() => {
      loadRealTimeStats();
      setLastUpdate(new Date());
    }, 30000);

    // Atualizar métricas completas a cada 5 minutos
    const metricsInterval = setInterval(loadMetrics, 5 * 60 * 1000);

    return () => {
      clearInterval(interval);
      clearInterval(metricsInterval);
    };
  }, []);

  const loadMetrics = async () => {
    try {
      const data = await monitoringService.getMetrics();
      setMetrics(data);
    } catch (error) {
      console.error('Erro ao carregar métricas:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadRealTimeStats = async () => {
    try {
      const stats = await monitoringService.getRealTimeStats();
      setRealTimeStats(stats);
    } catch (error) {
      console.error('Erro ao carregar stats em tempo real:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-8 h-8 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando métricas...</p>
        </div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <p className="text-gray-500">Erro ao carregar métricas</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Dashboard de Monitoramento</h2>
        <div className="text-sm text-gray-500">
          Última atualização: {lastUpdate.toLocaleTimeString('pt-BR')}
        </div>
      </div>

      {/* Status em tempo real */}
      {realTimeStats && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Status do Sistema</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(realTimeStats.systemStatus)}`}>
                {getStatusIcon(realTimeStats.systemStatus)}
                <span className="ml-2 capitalize">{realTimeStats.systemStatus}</span>
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{realTimeStats.activeUsers}</div>
              <div className="text-sm text-gray-500">Usuários ativos</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{realTimeStats.messagesLastHour}</div>
              <div className="text-sm text-gray-500">Mensagens/hora</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{metrics.avg_response_time}ms</div>
              <div className="text-sm text-gray-500">Tempo médio</div>
            </div>
          </div>
          {realTimeStats.lastError && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-700">
                <strong>Último erro:</strong> {realTimeStats.lastError}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Métricas principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Interações Totais</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.total_interactions}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Documentos</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.total_documents}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Conversas</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.total_conversations}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Taxa de Sucesso</p>
              <p className="text-2xl font-bold text-gray-900">{Math.round(metrics.success_rate * 100)}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Queries populares */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Perguntas Mais Frequentes</h3>
        <div className="space-y-3">
          {metrics.popular_queries.map((query, index) => (
            <div key={index} className="flex items-center justify-between">
              <span className="text-gray-700">{query.query}</span>
              <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                {query.count} vezes
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Estatísticas diárias */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Atividade dos Últimos 7 Dias</h3>
        <div className="space-y-4">
          {metrics.daily_stats.map((day, index) => (
            <div key={index} className="grid grid-cols-4 gap-4 py-2 border-b border-gray-100 last:border-b-0">
              <div className="font-medium text-gray-900">
                {new Date(day.date).toLocaleDateString('pt-BR', { 
                  weekday: 'short', 
                  day: '2-digit', 
                  month: '2-digit' 
                })}
              </div>
              <div className="text-center text-gray-600">{day.interactions} interações</div>
              <div className="text-center text-gray-600">{day.documents_uploaded} docs</div>
              <div className="text-center text-gray-600">{day.conversations_started} conversas</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
