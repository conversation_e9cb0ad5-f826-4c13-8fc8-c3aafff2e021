import { useState, useRef, useEffect } from 'react';
import { ChatInterface } from './components/ChatInterface';
import { DocumentInput } from './components/DocumentInput';
import { VereadoraHeader } from './components/VereadoraHeader';
import { TabNavigation } from './components/TabNavigation';
import { HistoryPanel } from './components/HistoryPanel';
import { DocumentManager } from './components/DocumentManager';
import { MonitoringDashboard } from './components/MonitoringDashboard';

import { vereadoraRAGService } from './services/vereadoraRAGService';
import { databaseService } from './services/databaseService';
import { monitoringService } from './services/monitoringService';
import { isSupabaseConfigured } from './config/supabase';
import { isGeminiConfigured } from './config/gemini';
import type { Message, Document, Conversation } from './types';

function App() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'chat' | 'history' | 'documents' | 'monitoring'>('chat');

  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    loadConversations();
    loadDocuments();
  }, []);

  const loadConversations = async () => {
    try {
      const convs = await databaseService.getConversations();
      setConversations(convs);
    } catch (error) {
      console.error('Erro ao carregar conversas:', error);
    }
  };

  const loadDocuments = async () => {
    try {
      const docs = await databaseService.getDocuments();
      setDocuments(docs);
    } catch (error) {
      console.error('Erro ao carregar documentos:', error);
    }
  };

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      // Registrar métricas
      await monitoringService.recordInteraction('message_sent', {
        messageLength: content.length,
        timestamp: new Date().toISOString()
      });

      const response = await vereadoraRAGService.processMessage(content);
      
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.answer,
        sender: 'assistant',
        timestamp: new Date(),
        sources: response.sources,
        confidence: response.confidence
      };

      setMessages(prev => [...prev, botMessage]);

      // Salvar conversa no banco
      if (currentConversationId) {
        await databaseService.addMessageToConversation(currentConversationId, userMessage);
        await databaseService.addMessageToConversation(currentConversationId, botMessage);
      } else {
        const conversation = await databaseService.createConversation(
          `Conversa ${new Date().toLocaleDateString()}`,
          [userMessage, botMessage]
        );
        setCurrentConversationId(conversation.id);
        await loadConversations();
      }

      // Registrar métricas de resposta
      await monitoringService.recordInteraction('message_received', {
        responseLength: response.answer.length,
        confidence: response.confidence,
        sourcesCount: response.sources?.length || 0,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Erro ao processar mensagem:', error);
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: 'Desculpe, ocorreu um erro ao processar sua mensagem. Tente novamente.',
        sender: 'assistant',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);

      // Registrar erro
      await monitoringService.recordInteraction('error', {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDocumentUpload = async (file: File) => {
    try {
      setIsLoading(true);
      
      await monitoringService.recordInteraction('document_upload', {
        fileName: file.name,
        fileSize: file.size,
        status: 'start',
        timestamp: new Date().toISOString()
      });

      const document = await vereadoraRAGService.uploadDocument(file);
      
      await loadDocuments();
      
      await monitoringService.recordInteraction('document_upload', {
        documentId: document.id,
        fileName: file.name,
        status: 'success',
        timestamp: new Date().toISOString()
      });

      // Adicionar mensagem de confirmação
      const confirmationMessage: Message = {
        id: Date.now().toString(),
        content: `✅ Documento "${file.name}" foi carregado com sucesso! Agora posso responder perguntas sobre seu conteúdo.`,
        sender: 'assistant',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, confirmationMessage]);

    } catch (error) {
      console.error('Erro ao fazer upload:', error);
      
      await monitoringService.recordInteraction('error', {
        fileName: file.name,
        type: 'document_upload',
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        timestamp: new Date().toISOString()
      });

      const errorMessage: Message = {
        id: Date.now().toString(),
        content: `❌ Erro ao carregar o documento "${file.name}". Tente novamente.`,
        sender: 'assistant',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoadConversation = async (conversationId: string) => {
    try {
      const conversation = await databaseService.getConversation(conversationId);
      if (conversation) {
        setMessages(conversation.messages);
        setCurrentConversationId(conversationId);
        setActiveTab('chat');
      }
    } catch (error) {
      console.error('Erro ao carregar conversa:', error);
    }
  };

  const handleNewConversation = () => {
    setMessages([]);
    setCurrentConversationId(null);
    setActiveTab('chat');
  };

  const handleDeleteConversation = async (conversationId: string) => {
    try {
      await databaseService.deleteConversation(conversationId);
      await loadConversations();
      
      if (currentConversationId === conversationId) {
        handleNewConversation();
      }
    } catch (error) {
      console.error('Erro ao deletar conversa:', error);
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      await databaseService.deleteDocument(documentId);
      await loadDocuments();
    } catch (error) {
      console.error('Erro ao deletar documento:', error);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'chat':
        return (
          <div className="flex flex-col h-full">
            <div className="flex-1 overflow-hidden">
              <ChatInterface
                messages={messages}
                onSendMessage={handleSendMessage}
                isLoading={isLoading}
                messagesEndRef={messagesEndRef}
              />
            </div>
            <div className="border-t border-gray-200 p-4">
              <DocumentInput onUpload={handleDocumentUpload} />
            </div>
          </div>
        );
      case 'history':
        return (
          <HistoryPanel
            conversations={conversations}
            onLoadConversation={handleLoadConversation}
            onNewConversation={handleNewConversation}
            onDeleteConversation={handleDeleteConversation}
            currentConversationId={currentConversationId}
          />
        );
      case 'documents':
        return (
          <DocumentManager
            documents={documents}
            onDeleteDocument={handleDeleteDocument}
            onUpload={handleDocumentUpload}
          />
        );

      case 'monitoring':
        return <MonitoringDashboard />;
      default:
        return null;
    }
  };

  const isDemoMode = !isSupabaseConfigured || !isGeminiConfigured;

  return (
    <div className="min-h-screen">
      <VereadoraHeader />

      {isDemoMode && (
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="glass-card p-6 mb-6 animate-fade-in-up">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center">
                  <svg className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-white mb-2">
                  🚀 Modo Demonstração Ativo
                </h3>
                <div className="text-white/90">
                  <p className="mb-3">
                    O sistema está funcionando em modo demonstração com todas as funcionalidades ativas!
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {!isSupabaseConfigured && (
                      <div className="flex items-center space-x-2 px-3 py-2 bg-white/10 rounded-lg">
                        <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                        <span className="text-sm">Configure Supabase para persistência</span>
                      </div>
                    )}
                    {!isGeminiConfigured && (
                      <div className="flex items-center space-x-2 px-3 py-2 bg-white/10 rounded-lg">
                        <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                        <span className="text-sm">Configure Gemini para IA avançada</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-6xl mx-auto px-4 py-6">
        <div className="card-modern overflow-hidden animate-scale-in">
          <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />

          <div className="h-[calc(100vh-280px)] bg-white/50 backdrop-blur-sm">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
