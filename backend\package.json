{"name": "vereadora-rafaela-whatsapp-backend", "version": "1.0.0", "description": "Backend WhatsApp para Assistente Virtual da Vereadora Rafaela de Nilda", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'No build step required'", "test": "echo 'No tests specified'"}, "keywords": ["whatsapp", "wppconnect", "chatbot", "vereadora", "parnam<PERSON>m", "rag", "ai"], "author": "Sistema RAG Vereadora Rafaela de Nilda", "license": "MIT", "dependencies": {"@wppconnect-team/wppconnect": "^1.30.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "moment": "^2.29.4", "qrcode": "^1.5.3", "winston": "^3.11.0", "node-cron": "^3.0.3", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}