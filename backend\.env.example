# Configurações do Servidor
PORT=3001
NODE_ENV=development

# Configurações do WhatsApp
WHATSAPP_SESSION_NAME=vereadora-rafaela
WHATSAPP_AUTO_CLOSE=false
WHATSAPP_DISABLE_WELCOME=false

# Configurações do Frontend (Sistema RAG)
FRONTEND_URL=http://localhost:3000
RAG_API_URL=http://localhost:3000/api

# Configurações de Segurança
JWT_SECRET=sua_chave_secreta_muito_forte_aqui
API_KEY=sua_api_key_para_autenticacao

# Configurações de Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Configurações de Log
LOG_LEVEL=info
LOG_FILE=logs/whatsapp-backend.log

# Configurações da Vereadora
VEREADORA_NAME=Rafaela de <PERSON>
VEREADORA_TITLE=Vereadora
MUNICIPIO=<PERSON><PERSON><PERSON><PERSON>=RN
GABINETE_TELEFONE=(84) 99999-9999
GABINETE_EMAIL=<EMAIL>

# Configurações de Horário de Atendimento
HORARIO_INICIO=08:00
HORARIO_FIM=18:00
DIAS_FUNCIONAMENTO=1,2,3,4,5

# Configurações de Mensagens Automáticas
AUTO_REPLY_ENABLED=true
WELCOME_MESSAGE_ENABLED=true
BUSINESS_HOURS_ONLY=false

# Configurações de Webhook (opcional)
WEBHOOK_URL=
WEBHOOK_SECRET=

# Configurações de Backup
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
