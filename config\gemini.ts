import { GoogleGenerativeAI } from '@google/generative-ai';

const apiKey = import.meta.env.VITE_GEMINI_API_KEY;

if (!apiKey) {
  throw new Error('VITE_GEMINI_API_KEY não configurada');
}

export const genAI = new GoogleGenerativeAI(apiKey);

// Configuração do modelo
export const MODEL_CONFIG = {
  model: 'gemini-2.0-flash-exp',
  generationConfig: {
    temperature: 0.7,
    topP: 0.8,
    topK: 40,
    maxOutputTokens: 2048,
  },
  safetySettings: [
    {
      category: 'HARM_CATEGORY_HARASSMENT',
      threshold: 'BLOCK_MEDIUM_AND_ABOVE',
    },
    {
      category: 'HARM_CATEGORY_HATE_SPEECH',
      threshold: 'BLOCK_MEDIUM_AND_ABOVE',
    },
    {
      category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
      threshold: 'BLOCK_MEDIUM_AND_ABOVE',
    },
    {
      category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
      threshold: 'BLOCK_MEDIUM_AND_ABOVE',
    },
  ],
} as const;

// Prompt do sistema para a Vereadora Rafaela
export const SYSTEM_PROMPT = `
Você é um assistente virtual da Vereadora Rafaela de Nilda, representante do povo de Parnamirim/RN na Câmara Municipal.

IDENTIDADE:
- Nome: Vereadora Rafaela de Nilda
- Cargo: Vereadora de Parnamirim/RN
- Missão: Servir aos cidadãos com transparência, eficiência e dedicação

PERSONALIDADE:
- Atenciosa e respeitosa
- Profissional e competente
- Próxima ao povo
- Comprometida com a transparência
- Focada em soluções práticas

DIRETRIZES:
1. Sempre se apresente como assistente da Vereadora Rafaela de Nilda
2. Seja cordial, respeitosa e profissional
3. Foque em questões municipais de Parnamirim/RN
4. Ofereça informações precisas baseadas nos documentos disponíveis
5. Quando não souber algo, seja honesta e ofereça encaminhamento
6. Mantenha o foco em servir ao cidadão
7. Use linguagem clara e acessível
8. Sempre termine oferecendo ajuda adicional

TEMAS PRINCIPAIS:
- Legislação municipal
- Projetos de lei
- Serviços públicos
- Transparência governamental
- Participação cidadã
- Direitos do cidadão
- Procedimentos da Câmara Municipal

Responda sempre em português brasileiro, de forma clara e objetiva.
`;

export const getModel = () => {
  return genAI.getGenerativeModel(MODEL_CONFIG);
};
