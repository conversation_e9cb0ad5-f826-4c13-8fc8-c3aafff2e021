import { GoogleGenerativeAI, GenerativeModel, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';

const apiKey = import.meta.env.VITE_GEMINI_API_KEY;

// Verificar se a API key está configurada
export const isGeminiConfigured = !!apiKey;

// Criar cliente apenas se configurado
export const genAI = isGeminiConfigured ? new GoogleGenerativeAI(apiKey) : null;

// Configurações dos modelos
export const MODELS = {
  // Modelo principal para chat
  CHAT: 'gemini-2.0-flash-exp',
  // Modelo para embeddings
  EMBEDDING: 'text-embedding-004',
  // Modelo para análise de documentos
  DOCUMENT_ANALYSIS: 'gemini-1.5-pro',
} as const;

// Configuração do modelo principal
export const MODEL_CONFIG = {
  model: MODELS.CHAT,
  generationConfig: {
    temperature: 0.7,
    topP: 0.8,
    topK: 40,
    maxOutputTokens: 2048,
    candidateCount: 1,
  },
  safetySettings: [
    {
      category: HarmCategory.HARM_CATEGORY_HARASSMENT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
    {
      category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
      threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
    },
  ],
} as const;

// Configuração para embeddings
export const EMBEDDING_CONFIG = {
  model: MODELS.EMBEDDING,
  taskType: 'RETRIEVAL_DOCUMENT',
  title: 'Documento da Vereadora Rafaela de Nilda'
} as const;

// Prompt do sistema para a Vereadora Rafaela
export const SYSTEM_PROMPT = `
Você é um assistente virtual da Vereadora Rafaela de Nilda, representante do povo de Parnamirim/RN na Câmara Municipal.

IDENTIDADE:
- Nome: Vereadora Rafaela de Nilda
- Cargo: Vereadora de Parnamirim/RN
- Missão: Servir aos cidadãos com transparência, eficiência e dedicação

PERSONALIDADE:
- Atenciosa e respeitosa
- Profissional e competente
- Próxima ao povo
- Comprometida com a transparência
- Focada em soluções práticas

DIRETRIZES:
1. Sempre se apresente como assistente da Vereadora Rafaela de Nilda
2. Seja cordial, respeitosa e profissional
3. Foque em questões municipais de Parnamirim/RN
4. Ofereça informações precisas baseadas nos documentos disponíveis
5. Quando não souber algo, seja honesta e ofereça encaminhamento
6. Mantenha o foco em servir ao cidadão
7. Use linguagem clara e acessível
8. Sempre termine oferecendo ajuda adicional

TEMAS PRINCIPAIS:
- Legislação municipal
- Projetos de lei
- Serviços públicos
- Transparência governamental
- Participação cidadã
- Direitos do cidadão
- Procedimentos da Câmara Municipal

Responda sempre em português brasileiro, de forma clara e objetiva.
`;

// Funções para obter modelos
export const getModel = (modelType: 'chat' | 'embedding' | 'document' = 'chat'): GenerativeModel => {
  if (!isGeminiConfigured || !genAI) {
    throw new Error('Gemini não configurado');
  }

  switch (modelType) {
    case 'chat':
      return genAI.getGenerativeModel(MODEL_CONFIG);
    case 'embedding':
      return genAI.getGenerativeModel({ model: MODELS.EMBEDDING });
    case 'document':
      return genAI.getGenerativeModel({
        model: MODELS.DOCUMENT_ANALYSIS,
        generationConfig: {
          temperature: 0.3,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 4096,
        }
      });
    default:
      return genAI.getGenerativeModel(MODEL_CONFIG);
  }
};

// Utilitários para Gemini
export const geminiUtils = {
  // Testar conexão
  async testConnection(): Promise<boolean> {
    if (!isGeminiConfigured) return false;

    try {
      const model = getModel('chat');
      const result = await model.generateContent('Teste de conexão');
      return !!result.response.text();
    } catch {
      return false;
    }
  },

  // Contar tokens
  async countTokens(text: string): Promise<number> {
    if (!isGeminiConfigured) return 0;

    try {
      const model = getModel('chat');
      const result = await model.countTokens(text);
      return result.totalTokens;
    } catch {
      return 0;
    }
  },

  // Gerar embedding
  async generateEmbedding(text: string): Promise<number[]> {
    if (!isGeminiConfigured) {
      throw new Error('Gemini não configurado');
    }

    try {
      const model = getModel('embedding');
      const result = await model.embedContent(text);
      return result.embedding.values;
    } catch (error) {
      console.error('Erro ao gerar embedding:', error);
      throw error;
    }
  },

  // Analisar documento
  async analyzeDocument(content: string): Promise<string> {
    if (!isGeminiConfigured) {
      throw new Error('Gemini não configurado');
    }

    try {
      const model = getModel('document');
      const prompt = `
        Analise o seguinte documento e forneça um resumo estruturado:

        ${content}

        Forneça:
        1. Resumo executivo
        2. Principais tópicos
        3. Informações relevantes para cidadãos de Parnamirim/RN
        4. Ações ou decisões importantes
      `;

      const result = await model.generateContent(prompt);
      return result.response.text();
    } catch (error) {
      console.error('Erro ao analisar documento:', error);
      throw error;
    }
  },

  // Verificar configuração
  getConfig() {
    return {
      isConfigured: isGeminiConfigured,
      models: MODELS,
      hasApiKey: !!apiKey,
      apiKeyPrefix: apiKey ? `${apiKey.substring(0, 10)}...` : null
    };
  }
};
