export interface Message {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
  sources?: Source[];
  confidence?: number;
}

export interface Source {
  id: string;
  title: string;
  content: string;
  relevance: number;
  document_id?: string;
}

export interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  upload_date: Date;
  status: 'processing' | 'ready' | 'error';
  chunks_count?: number;
}

export interface Conversation {
  id: string;
  title: string;
  created_at: Date;
  updated_at: Date;
  messages: Message[];
  message_count: number;
}

export interface RAGResponse {
  answer: string;
  sources: Source[];
  confidence: number;
  processing_time: number;
}

export interface VectorSearchResult {
  content: string;
  metadata: {
    document_id: string;
    document_name: string;
    chunk_index: number;
    page_number?: number;
  };
  similarity: number;
}

export interface EmbeddingResult {
  embedding: number[];
  text: string;
  metadata?: Record<string, any>;
}

export interface DocumentChunk {
  id: string;
  document_id: string;
  content: string;
  chunk_index: number;
  embedding?: number[];
  metadata: {
    page_number?: number;
    section?: string;
    [key: string]: any;
  };
}

export interface ProcessingStatus {
  status: 'idle' | 'processing' | 'completed' | 'error';
  progress: number;
  message: string;
  details?: string;
}

export interface MonitoringMetrics {
  total_interactions: number;
  total_documents: number;
  total_conversations: number;
  avg_response_time: number;
  success_rate: number;
  error_rate: number;
  popular_queries: Array<{
    query: string;
    count: number;
  }>;
  daily_stats: Array<{
    date: string;
    interactions: number;
    documents_uploaded: number;
    conversations_started: number;
  }>;
}

export interface InteractionLog {
  id: string;
  type: 'message_sent' | 'message_received' | 'document_upload' | 'error';
  timestamp: Date;
  metadata: Record<string, any>;
}

// WhatsApp Types
export type WhatsAppStatus = 
  | 'disconnected' 
  | 'connecting' 
  | 'connected' 
  | 'error'
  | 'requires_backend'
  | 'connecting_qr'
  | 'loading';

export interface WhatsAppMessage {
  id: string;
  text: string;
  sender: string;
  timestamp: Date;
  isFromMe: boolean;
}

export interface WhatsAppContact {
  id: string;
  name: string;
  phone: string;
  lastMessage?: string;
  lastMessageTime?: Date;
  messageCount: number;
}

// Configuration Types
export interface VereadoraConfig {
  name: string;
  city: string;
  state: string;
  description: string;
  contact: {
    phone: string;
    email: string;
    address: string;
  };
  social: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
  };
}

// API Response Types
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
