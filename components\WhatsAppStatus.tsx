import React, { useState, useEffect } from 'react';
import { whatsappService, WhatsAppStatus as IWhatsAppStatus, WhatsAppStats } from '../services/whatsappService';

export const WhatsAppStatus: React.FC = () => {
  const [status, setStatus] = useState<IWhatsAppStatus | null>(null);
  const [stats, setStats] = useState<WhatsAppStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showQR, setShowQR] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [backendAvailable, setBackendAvailable] = useState(false);

  useEffect(() => {
    checkBackendAndLoadStatus();
    
    // Polling para atualizações
    const interval = setInterval(checkBackendAndLoadStatus, 5000);
    
    return () => clearInterval(interval);
  }, []);

  const checkBackendAndLoadStatus = async () => {
    try {
      setError(null);
      
      // Verificar se backend está disponível
      const isAvailable = await whatsappService.isBackendAvailable();
      setBackendAvailable(isAvailable);
      
      if (!isAvailable) {
        setError('Backend WhatsApp não está rodando');
        setLoading(false);
        return;
      }

      // Carregar status
      const currentStatus = await whatsappService.getStatus();
      setStatus(currentStatus);

      // Carregar estatísticas se conectado
      if (currentStatus?.isConnected) {
        const currentStats = await whatsappService.getStats();
        setStats(currentStats);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao conectar com backend');
    } finally {
      setLoading(false);
    }
  };

  const handleRestart = async () => {
    setLoading(true);
    try {
      const success = await whatsappService.restart();
      if (success) {
        setTimeout(checkBackendAndLoadStatus, 2000); // Aguardar reinicialização
      } else {
        setError('Falha ao reiniciar WhatsApp');
      }
    } catch (err) {
      setError('Erro ao reiniciar WhatsApp');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = () => {
    if (loading) {
      return (
        <div className="loading-modern">
          <div className="loading-dot"></div>
          <div className="loading-dot"></div>
          <div className="loading-dot"></div>
        </div>
      );
    }

    if (!backendAvailable) {
      return <span className="text-gray-400">⚪</span>;
    }

    if (status?.isConnected) {
      return <span className="text-green-500">🟢</span>;
    }

    if (status?.qrCode) {
      return <span className="text-orange-500 animate-pulse">📱</span>;
    }

    return <span className="text-red-500">🔴</span>;
  };

  const getStatusText = () => {
    if (loading) return 'Verificando...';
    if (!backendAvailable) return 'Backend não disponível';
    if (error) return error;
    if (status?.isConnected) return 'WhatsApp Conectado';
    if (status?.qrCode) return 'Aguardando QR Code';
    return 'Desconectado';
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  return (
    <div className="glass-card p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <div>
            <h3 className="font-semibold text-gray-800 flex items-center space-x-2">
              <span>📱 WhatsApp Backend</span>
              {status?.isConnected && (
                <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                  Online
                </span>
              )}
            </h3>
            <p className="text-sm text-gray-600">{getStatusText()}</p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          {status?.qrCode && !status.isConnected && (
            <button
              onClick={() => setShowQR(!showQR)}
              className="px-3 py-1 text-sm bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
            >
              {showQR ? 'Ocultar QR' : 'Mostrar QR'}
            </button>
          )}
          
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="px-3 py-1 text-sm bg-white/30 hover:bg-white/50 rounded-lg transition-colors"
          >
            {showDetails ? 'Ocultar' : 'Detalhes'}
          </button>
          
          {backendAvailable && (
            <button
              onClick={handleRestart}
              disabled={loading}
              className="px-3 py-1 text-sm bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              🔄 Reiniciar
            </button>
          )}
        </div>
      </div>

      {/* QR Code */}
      {showQR && status?.qrCode && !status.isConnected && (
        <div className="mb-4 p-4 bg-white rounded-lg border-2 border-orange-200">
          <div className="text-center">
            <h4 className="font-medium text-gray-800 mb-3">
              📱 Escaneie o QR Code com WhatsApp
            </h4>
            
            {status.qrCode.base64 ? (
              <div className="flex flex-col items-center space-y-3">
                <img 
                  src={status.qrCode.base64} 
                  alt="QR Code WhatsApp"
                  className="w-48 h-48 border border-gray-200 rounded-lg"
                />
                <p className="text-xs text-gray-500">
                  Gerado em: {new Date(status.qrCode.timestamp).toLocaleString('pt-BR')}
                </p>
                <div className="flex space-x-2">
                  <a
                    href={`${whatsappService.getBackendURL()}${status.qrCode.path}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="px-3 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors"
                  >
                    🔗 Abrir em Nova Aba
                  </a>
                  <button
                    onClick={() => navigator.clipboard.writeText(status.qrCode?.base64 || '')}
                    className="px-3 py-1 text-xs bg-gray-500 hover:bg-gray-600 text-white rounded transition-colors"
                  >
                    📋 Copiar Base64
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-gray-500">
                <div className="loading-modern mb-2">
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                </div>
                <p>Gerando QR Code...</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Detalhes */}
      {showDetails && (
        <div className="space-y-4 pt-4 border-t border-white/20">
          {/* Informações de Conexão */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-700">Status da Conexão</p>
              <div className="space-y-1 text-xs text-gray-600">
                <p>Sessão: {status?.sessionName || 'N/A'}</p>
                <p>Tentativas: {status?.connectionAttempts || 0}</p>
                <p>Backend: {whatsappService.getBackendURL()}</p>
              </div>
            </div>
            
            {stats && (
              <div>
                <p className="text-sm font-medium text-gray-700">Estatísticas</p>
                <div className="space-y-1 text-xs text-gray-600">
                  <p>Uptime: {formatUptime(stats.uptime)}</p>
                  <p>Sessões ativas: {stats.messages.activeSessions}</p>
                  <p>Total mensagens: {stats.messages.totalMessages}</p>
                </div>
              </div>
            )}
          </div>

          {/* Funcionalidades */}
          <div>
            <p className="text-sm font-medium text-gray-700 mb-2">Funcionalidades WhatsApp:</p>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className={`flex items-center space-x-1 ${status?.isConnected ? 'text-green-600' : 'text-gray-400'}`}>
                <span>{status?.isConnected ? '✅' : '❌'}</span>
                <span>Envio de mensagens</span>
              </div>
              <div className={`flex items-center space-x-1 ${status?.isConnected ? 'text-green-600' : 'text-gray-400'}`}>
                <span>{status?.isConnected ? '✅' : '❌'}</span>
                <span>Recebimento automático</span>
              </div>
              <div className={`flex items-center space-x-1 ${stats?.messages.autoReplyEnabled ? 'text-green-600' : 'text-gray-400'}`}>
                <span>{stats?.messages.autoReplyEnabled ? '✅' : '❌'}</span>
                <span>Respostas automáticas</span>
              </div>
              <div className={`flex items-center space-x-1 ${stats?.messages.isBusinessHours ? 'text-green-600' : 'text-orange-500'}`}>
                <span>{stats?.messages.isBusinessHours ? '🟢' : '🟡'}</span>
                <span>Horário comercial</span>
              </div>
            </div>
          </div>

          {/* Ações */}
          <div className="flex space-x-2 pt-3">
            <button
              onClick={checkBackendAndLoadStatus}
              className="px-3 py-1 text-xs bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
            >
              🔄 Atualizar Status
            </button>
            
            {!backendAvailable && (
              <div className="flex-1 text-xs text-gray-600">
                <p>💡 Para usar WhatsApp:</p>
                <p>1. Vá para a pasta backend/</p>
                <p>2. Execute: npm run dev</p>
                <p>3. O backend rodará na porta 3001</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
