import React, { useState, useEffect } from 'react';
import { whatsappService, WhatsAppStats, SendMessageRequest } from '../services/whatsappService';

export const WhatsAppManager: React.FC = () => {
  const [stats, setStats] = useState<WhatsAppStats | null>(null);
  const [chats, setChats] = useState<any[]>([]);
  const [contacts, setContacts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'chats' | 'contacts' | 'send'>('overview');
  const [sendMessage, setSendMessage] = useState<SendMessageRequest>({
    to: '',
    message: '',
    type: 'text'
  });
  const [sendResult, setSendResult] = useState<string | null>(null);

  useEffect(() => {
    loadData();
    const interval = setInterval(loadData, 10000); // Atualizar a cada 10s
    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      const [statsData, chatsData, contactsData] = await Promise.all([
        whatsappService.getStats(),
        whatsappService.getChats(),
        whatsappService.getContacts()
      ]);

      setStats(statsData);
      setChats(chatsData);
      setContacts(contactsData);
    } catch (error) {
      console.error('Erro ao carregar dados WhatsApp:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!sendMessage.to || !sendMessage.message) {
      setSendResult('❌ Preencha número e mensagem');
      return;
    }

    try {
      setSendResult('📤 Enviando...');
      const result = await whatsappService.sendMessage(sendMessage);
      
      if (result.success) {
        setSendResult('✅ Mensagem enviada com sucesso!');
        setSendMessage({ to: '', message: '', type: 'text' });
      } else {
        setSendResult(`❌ Erro: ${result.error}`);
      }
    } catch (error) {
      setSendResult('❌ Erro ao enviar mensagem');
    }

    // Limpar resultado após 5 segundos
    setTimeout(() => setSendResult(null), 5000);
  };

  const formatPhoneNumber = (phone: string) => {
    return phone.replace('@c.us', '').replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  };

  const tabs = [
    { id: 'overview', label: '📊 Visão Geral', icon: '📊' },
    { id: 'chats', label: '💬 Conversas', icon: '💬' },
    { id: 'contacts', label: '👥 Contatos', icon: '👥' },
    { id: 'send', label: '📤 Enviar', icon: '📤' }
  ];

  if (loading && !stats) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="loading-modern mb-4">
            <div className="loading-dot"></div>
            <div className="loading-dot"></div>
            <div className="loading-dot"></div>
          </div>
          <p className="text-gray-600">Carregando dados do WhatsApp...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header com Tabs */}
      <div className="border-b border-white/20 bg-white/60 backdrop-blur-sm">
        <div className="flex space-x-1 p-4">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-orange-500 text-white shadow-lg'
                  : 'bg-white/30 text-gray-700 hover:bg-white/50'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Conteúdo */}
      <div className="flex-1 p-6 overflow-auto">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-800">📊 Visão Geral do WhatsApp</h2>
            
            {stats ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Status da Conexão */}
                <div className="glass-card p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className={`text-2xl ${stats.connection.isConnected ? 'text-green-500' : 'text-red-500'}`}>
                      {stats.connection.isConnected ? '🟢' : '🔴'}
                    </span>
                    <div>
                      <h3 className="font-medium text-gray-800">Conexão</h3>
                      <p className="text-sm text-gray-600">
                        {stats.connection.isConnected ? 'Conectado' : 'Desconectado'}
                      </p>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 space-y-1">
                    <p>Sessão: {stats.connection.sessionName}</p>
                    <p>Tentativas: {stats.connection.connectionAttempts}</p>
                  </div>
                </div>

                {/* Estatísticas de Mensagens */}
                <div className="glass-card p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="text-2xl">💬</span>
                    <div>
                      <h3 className="font-medium text-gray-800">Mensagens</h3>
                      <p className="text-sm text-gray-600">{stats.messages.totalMessages} total</p>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 space-y-1">
                    <p>Sessões ativas: {stats.messages.activeSessions}</p>
                    <p>Rate limited: {stats.messages.rateLimitedUsers}</p>
                  </div>
                </div>

                {/* Sistema */}
                <div className="glass-card p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="text-2xl">⚙️</span>
                    <div>
                      <h3 className="font-medium text-gray-800">Sistema</h3>
                      <p className="text-sm text-gray-600">
                        Uptime: {Math.floor(stats.uptime / 3600)}h {Math.floor((stats.uptime % 3600) / 60)}m
                      </p>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 space-y-1">
                    <p>Auto-reply: {stats.messages.autoReplyEnabled ? '✅' : '❌'}</p>
                    <p>Horário comercial: {stats.messages.isBusinessHours ? '🟢' : '🟡'}</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="glass-card p-8 text-center">
                <span className="text-4xl mb-4 block">📱</span>
                <h3 className="text-lg font-medium text-gray-800 mb-2">WhatsApp Backend Offline</h3>
                <p className="text-gray-600 mb-4">
                  O backend WhatsApp não está disponível. Inicie o servidor para usar esta funcionalidade.
                </p>
                <div className="text-sm text-gray-500 space-y-1">
                  <p>1. Vá para a pasta backend/</p>
                  <p>2. Execute: npm run dev</p>
                  <p>3. O backend rodará na porta 3001</p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'chats' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-800">💬 Conversas Ativas</h2>
              <button
                onClick={loadData}
                className="px-3 py-1 text-sm bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
              >
                🔄 Atualizar
              </button>
            </div>

            {chats.length > 0 ? (
              <div className="space-y-2">
                {chats.map((chat, index) => (
                  <div key={chat.id || index} className="glass-card p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">
                          {chat.isGroup ? '👥' : '👤'}
                        </span>
                        <div>
                          <h3 className="font-medium text-gray-800">{chat.name || 'Sem nome'}</h3>
                          <p className="text-sm text-gray-600">
                            {chat.isGroup ? 'Grupo' : 'Contato individual'}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        {chat.unreadCount > 0 && (
                          <span className="px-2 py-1 text-xs bg-orange-500 text-white rounded-full">
                            {chat.unreadCount}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="glass-card p-8 text-center">
                <span className="text-4xl mb-4 block">💬</span>
                <p className="text-gray-600">Nenhuma conversa encontrada</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'contacts' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-800">👥 Contatos ({contacts.length})</h2>
              <button
                onClick={loadData}
                className="px-3 py-1 text-sm bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
              >
                🔄 Atualizar
              </button>
            </div>

            {contacts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {contacts.slice(0, 50).map((contact, index) => (
                  <div key={contact.id || index} className="glass-card p-3">
                    <div className="flex items-center space-x-3">
                      <span className="text-xl">👤</span>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-800 truncate">
                          {contact.name || 'Sem nome'}
                        </h3>
                        <p className="text-xs text-gray-600 truncate">
                          {formatPhoneNumber(contact.id)}
                        </p>
                        <div className="flex space-x-1 mt-1">
                          {contact.isMyContact && (
                            <span className="px-1 py-0.5 text-xs bg-green-100 text-green-800 rounded">
                              Contato
                            </span>
                          )}
                          {contact.isWAContact && (
                            <span className="px-1 py-0.5 text-xs bg-blue-100 text-blue-800 rounded">
                              WhatsApp
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="glass-card p-8 text-center">
                <span className="text-4xl mb-4 block">👥</span>
                <p className="text-gray-600">Nenhum contato encontrado</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'send' && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-800">📤 Enviar Mensagem</h2>
            
            <div className="glass-card p-6 max-w-2xl">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Número de Destino
                  </label>
                  <input
                    type="text"
                    value={sendMessage.to}
                    onChange={(e) => setSendMessage(prev => ({ ...prev, to: e.target.value }))}
                    placeholder="5584999999999 (com código do país)"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Formato: código do país + DDD + número (ex: 5584999999999)
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mensagem
                  </label>
                  <textarea
                    value={sendMessage.message}
                    onChange={(e) => setSendMessage(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Digite sua mensagem aqui..."
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  />
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={handleSendMessage}
                    disabled={!sendMessage.to || !sendMessage.message || !stats?.connection.isConnected}
                    className="px-6 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    📤 Enviar Mensagem
                  </button>
                  
                  <button
                    onClick={() => setSendMessage({ to: '', message: '', type: 'text' })}
                    className="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  >
                    🗑️ Limpar
                  </button>
                </div>

                {sendResult && (
                  <div className={`p-3 rounded-lg ${
                    sendResult.includes('✅') ? 'bg-green-100 text-green-800' : 
                    sendResult.includes('📤') ? 'bg-blue-100 text-blue-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {sendResult}
                  </div>
                )}

                {!stats?.connection.isConnected && (
                  <div className="p-3 bg-orange-100 text-orange-800 rounded-lg">
                    ⚠️ WhatsApp não está conectado. Conecte primeiro para enviar mensagens.
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
