import { getModel, SYSTEM_PROMPT, isGeminiConfigured } from '../config/gemini';
import { databaseService } from './databaseService';
import { embeddingService } from './embeddingService';
import { documentProcessor } from './documentProcessor';
import type { RAGResponse, Document, Source } from '../types';

class VereadoraRAGService {
  private model = isGeminiConfigured ? getModel() : null;

  async processMessage(userMessage: string): Promise<RAGResponse> {
    const startTime = Date.now();

    try {
      console.log('🔍 Processando mensagem:', userMessage);

      // Se Gemini não estiver configurado, usar resposta simulada
      if (!isGeminiConfigured || !this.model) {
        return this.generateFallbackResponse(userMessage, startTime);
      }

      // 1. Gerar embedding da pergunta
      const questionEmbedding = await embeddingService.generateEmbedding(userMessage);

      // 2. Buscar chunks relevantes
      const relevantChunks = await databaseService.searchSimilarChunks(
        questionEmbedding.embedding,
        5
      );

      console.log(`📚 Encontrados ${relevantChunks.length} chunks relevantes`);

      // 3. Preparar contexto
      const context = relevantChunks
        .map(chunk => `[Documento: ${chunk.metadata.document_name}]\n${chunk.content}`)
        .join('\n\n---\n\n');

      // 4. Preparar prompt
      const prompt = this.buildPrompt(userMessage, context);

      // 5. Gerar resposta
      const result = await this.model.generateContent(prompt);
      const response = result.response;
      const answer = response.text();

      // 6. Preparar sources
      const sources: Source[] = relevantChunks.map((chunk, index) => ({
        id: chunk.id,
        title: chunk.metadata.document_name || `Documento ${index + 1}`,
        content: chunk.content.substring(0, 200) + '...',
        relevance: 0.8, // Placeholder - em produção calcular similaridade real
        document_id: chunk.document_id
      }));

      // 7. Calcular confiança
      const confidence = this.calculateConfidence(relevantChunks.length, answer);

      const processingTime = Date.now() - startTime;

      console.log(`✅ Resposta gerada em ${processingTime}ms`);

      return {
        answer,
        sources,
        confidence,
        processing_time: processingTime
      };

    } catch (error) {
      console.error('❌ Erro ao processar mensagem:', error);
      
      return {
        answer: 'Desculpe, ocorreu um erro ao processar sua pergunta. Tente novamente ou reformule sua pergunta.',
        sources: [],
        confidence: 0,
        processing_time: Date.now() - startTime
      };
    }
  }

  async uploadDocument(file: File): Promise<Document> {
    console.log('📄 Iniciando upload do documento:', file.name);

    try {
      // 1. Salvar documento no banco
      const document = await databaseService.saveDocument({
        name: file.name,
        type: file.type,
        size: file.size,
        upload_date: new Date(),
        status: 'processing'
      });

      console.log('💾 Documento salvo no banco:', document.id);

      // 2. Processar documento em background
      this.processDocumentAsync(document, file);

      return document;

    } catch (error) {
      console.error('❌ Erro no upload:', error);
      throw error;
    }
  }

  private async processDocumentAsync(document: Document, file: File): Promise<void> {
    try {
      console.log('🔄 Processando documento:', document.name);

      // 1. Extrair texto do PDF
      const text = await documentProcessor.extractTextFromPDF(file);
      console.log(`📝 Texto extraído: ${text.length} caracteres`);

      // 2. Dividir em chunks
      const chunks = documentProcessor.chunkText(text, {
        chunkSize: 1000,
        overlap: 200
      });
      console.log(`📦 Criados ${chunks.length} chunks`);

      // 3. Gerar embeddings para cada chunk
      const chunksWithEmbeddings = [];
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        console.log(`🧮 Gerando embedding ${i + 1}/${chunks.length}`);
        
        const embedding = await embeddingService.generateEmbedding(chunk);
        
        chunksWithEmbeddings.push({
          document_id: document.id,
          content: chunk,
          chunk_index: i,
          embedding: embedding.embedding,
          metadata: {
            page_number: Math.floor(i / 3) + 1, // Estimativa
            section: `Seção ${i + 1}`
          }
        });

        // Pequena pausa para não sobrecarregar a API
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 4. Salvar chunks no banco
      await databaseService.saveDocumentChunks(chunksWithEmbeddings);
      console.log('💾 Chunks salvos no banco');

      // 5. Atualizar status do documento
      await databaseService.updateDocumentStatus(document.id, 'ready', chunks.length);
      console.log('✅ Documento processado com sucesso');

    } catch (error) {
      console.error('❌ Erro no processamento:', error);
      await databaseService.updateDocumentStatus(document.id, 'error');
    }
  }

  private buildPrompt(question: string, context: string): string {
    return `${SYSTEM_PROMPT}

CONTEXTO DISPONÍVEL:
${context || 'Nenhum documento específico encontrado.'}

PERGUNTA DO CIDADÃO:
${question}

INSTRUÇÕES:
1. Use o contexto fornecido para responder à pergunta
2. Se o contexto não contém informações suficientes, seja honesta sobre isso
3. Mantenha o foco em questões municipais de Parnamirim/RN
4. Seja clara, objetiva e útil
5. Ofereça encaminhamentos quando apropriado
6. Termine oferecendo ajuda adicional

RESPOSTA:`;
  }

  private calculateConfidence(chunksFound: number, answer: string): number {
    let confidence = 0.5; // Base

    // Aumentar confiança baseado em chunks encontrados
    if (chunksFound > 0) confidence += 0.2;
    if (chunksFound > 2) confidence += 0.1;
    if (chunksFound > 4) confidence += 0.1;

    // Aumentar confiança baseado no tamanho da resposta
    if (answer.length > 100) confidence += 0.1;
    if (answer.length > 300) confidence += 0.1;

    // Diminuir confiança se resposta contém palavras de incerteza
    const uncertaintyWords = ['não sei', 'não tenho', 'desculpe', 'talvez', 'possivelmente'];
    const hasUncertainty = uncertaintyWords.some(word => 
      answer.toLowerCase().includes(word)
    );
    
    if (hasUncertainty) confidence -= 0.2;

    return Math.max(0, Math.min(1, confidence));
  }

  // Método de fallback quando APIs não estão configuradas
  private generateFallbackResponse(userMessage: string, startTime: number): RAGResponse {
    const responseTime = Date.now() - startTime;

    // Respostas pré-definidas baseadas em palavras-chave
    const responses = this.getFallbackResponses();
    const messageKey = userMessage.toLowerCase();

    let answer = responses.default;

    // Buscar resposta baseada em palavras-chave
    for (const [keywords, response] of Object.entries(responses.keywords)) {
      if (keywords.split(',').some(keyword => messageKey.includes(keyword.trim()))) {
        answer = response;
        break;
      }
    }

    return {
      answer,
      sources: [],
      confidence: 0.6,
      processing_time: responseTime
    };
  }

  private getFallbackResponses() {
    return {
      default: `Olá! Sou o assistente virtual da Vereadora Rafaela de Nilda.

🏛️ **Sobre o Gabinete:**
Estou aqui para ajudar com informações sobre o mandato da Vereadora Rafaela de Nilda na Câmara Municipal de Parnamirim/RN.

⚠️ **Modo Demonstração:**
No momento, estou funcionando em modo demonstração. Para acesso completo às funcionalidades e documentos oficiais, é necessário configurar as integrações com os serviços externos.

📞 **Contato Direto:**
Para questões específicas ou urgentes, entre em contato diretamente com o gabinete:
- 📧 Email: <EMAIL>
- 📱 WhatsApp: (84) 99999-9999

Como posso ajudá-lo hoje?`,

      keywords: {
        'projeto,lei,legislação': `📋 **Projetos de Lei**

A Vereadora Rafaela de Nilda trabalha constantemente na elaboração e análise de projetos de lei que beneficiem os cidadãos de Parnamirim.

🔍 **Para consultar projetos específicos:**
- Acesse o site da Câmara Municipal de Parnamirim
- Entre em contato com o gabinete para informações detalhadas

📞 **Contato:** (84) 99999-9999`,

        'horário,atendimento,funcionamento': `🕐 **Horário de Atendimento**

**Gabinete da Vereadora Rafaela de Nilda:**
- Segunda a Sexta: 8h às 17h
- Local: Câmara Municipal de Parnamirim/RN

📞 **Agendamentos:**
- Telefone: (84) 99999-9999
- Email: <EMAIL>

💡 **Dica:** Recomendamos agendar previamente para garantir o atendimento personalizado.`,

        'serviço,público,parnamirim': `🏛️ **Serviços Públicos em Parnamirim**

A Vereadora Rafaela de Nilda atua na fiscalização e melhoria dos serviços públicos municipais:

🚌 **Transporte Público**
🏥 **Saúde Municipal**
📚 **Educação**
🛣️ **Infraestrutura**
🌳 **Meio Ambiente**

📞 **Para denúncias ou sugestões:**
Entre em contato com o gabinete: (84) 99999-9999`,

        'transparência,prestação,contas': `📊 **Transparência e Prestação de Contas**

A Vereadora Rafaela de Nilda preza pela transparência total em seu mandato:

🔍 **Portal da Transparência:**
- Gastos do gabinete
- Projetos apresentados
- Votações e posicionamentos

📱 **Redes Sociais:**
Acompanhe as atividades diárias e relatórios de trabalho.

📞 **Contato:** (84) 99999-9999 para esclarecimentos adicionais.`
      }
    };
  }

  // Método para busca direta (sem RAG)
  async simpleQuery(question: string): Promise<string> {
    try {
      if (!isGeminiConfigured || !this.model) {
        const fallbackResponse = this.generateFallbackResponse(question, Date.now());
        return fallbackResponse.answer;
      }

      const prompt = `${SYSTEM_PROMPT}

PERGUNTA: ${question}

Responda como assistente da Vereadora Rafaela de Nilda, mesmo sem documentos específicos.`;

      const result = await this.model.generateContent(prompt);
      return result.response.text();
    } catch (error) {
      console.error('Erro na consulta simples:', error);
      return 'Desculpe, não foi possível processar sua pergunta no momento.';
    }
  }
}

export const vereadoraRAGService = new VereadoraRAGService();
