import { getModel, SYSTEM_PROMPT, isGeminiConfigured } from '../config/gemini';
import { databaseService } from './databaseService';
import { embeddingService } from './embeddingService';
import { documentProcessor } from './documentProcessor';
import { semanticSearchService } from './semanticSearchService';
import { cacheService } from './cacheService';
import type { RAGResponse, Document, Source } from '../types';

class VereadoraRAGService {
  private model = isGeminiConfigured ? getModel() : null;

  async processMessage(userMessage: string): Promise<RAGResponse> {
    const startTime = Date.now();

    try {
      console.log('🔍 Processando mensagem:', userMessage);

      // Se Gemini não estiver configurado, usar resposta simulada
      if (!isGeminiConfigured || !this.model) {
        return this.generateFallbackResponse(userMessage, startTime);
      }

      // 1. Verificar cache de resposta primeiro
      const contextHash = await this.generateContextHash(userMessage);
      const cachedResponse = await cacheService.getResponse(userMessage, contextHash);
      if (cachedResponse) {
        console.log('⚡ Resposta encontrada no cache');
        return cachedResponse;
      }

      // 2. Buscar chunks relevantes com busca semântica avançada
      const relevantChunks = await semanticSearchService.searchSimilarChunks(userMessage, {
        limit: 5,
        minSimilarity: 0.3,
        boostRecent: true
      });

      console.log(`📚 Encontrados ${relevantChunks.length} chunks relevantes`);

      // 3. Preparar contexto
      const context = relevantChunks
        .map(chunk => `[Documento: ${chunk.metadata.document_name}]\n${chunk.content}`)
        .join('\n\n---\n\n');

      // 4. Preparar prompt
      const prompt = this.buildPrompt(userMessage, context);

      // 5. Gerar resposta
      const result = await this.model.generateContent(prompt);
      const geminiResponse = result.response;
      const answer = geminiResponse.text();

      // 6. Preparar sources com relevância real
      const sources: Source[] = relevantChunks.map((chunk, index) => ({
        id: chunk.id,
        title: chunk.metadata.document_name || `Documento ${index + 1}`,
        content: chunk.content.substring(0, 200) + '...',
        relevance: chunk.metadata.similarity || 0.8, // Usar similaridade real se disponível
        document_id: chunk.document_id
      }));

      // 7. Calcular confiança
      const confidence = this.calculateConfidence(relevantChunks.length, answer);

      const processingTime = Date.now() - startTime;

      console.log(`✅ Resposta gerada em ${processingTime}ms`);

      const ragResponse: RAGResponse = {
        answer,
        sources,
        confidence,
        processing_time: processingTime
      };

      // 8. Salvar resposta no cache
      await cacheService.setResponse(userMessage, contextHash, ragResponse);

      return ragResponse;

    } catch (error) {
      console.error('❌ Erro ao processar mensagem:', error);
      
      return {
        answer: 'Desculpe, ocorreu um erro ao processar sua pergunta. Tente novamente ou reformule sua pergunta.',
        sources: [],
        confidence: 0,
        processing_time: Date.now() - startTime
      };
    }
  }

  async uploadDocument(file: File): Promise<Document> {
    console.log('📄 Iniciando upload do documento:', file.name);

    try {
      // 1. Validar arquivo
      if (file.size > 10 * 1024 * 1024) { // 10MB
        throw new Error('Arquivo muito grande. Máximo: 10MB');
      }

      // 2. Salvar documento no banco
      const document = await databaseService.saveDocument({
        name: file.name,
        type: file.type,
        size: file.size,
        upload_date: new Date(),
        status: 'processing'
      });

      console.log('💾 Documento salvo no banco:', document.id);

      // 3. Processar documento em background com tratamento de erro
      this.processDocumentAsync(document, file)
        .then(() => {
          console.log('✅ Processamento concluído para:', document.name);
        })
        .catch(error => {
          console.error('❌ Erro no processamento assíncrono:', error);
          // Atualizar status para erro
          databaseService.updateDocumentStatus(document.id, 'error').catch(console.error);
        });

      return document;

    } catch (error) {
      console.error('❌ Erro no upload:', error);
      throw new Error(`Falha no upload: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  private async processDocumentAsync(document: Document, file: File): Promise<void> {
    try {
      console.log('🔄 Processando documento:', document.name);

      // Atualizar status para "processing"
      await databaseService.updateDocumentStatus(document.id, 'processing');

      let text: string;

      // 1. Extrair texto baseado no tipo de arquivo
      if (file.type === 'application/pdf') {
        try {
          text = await documentProcessor.extractTextFromPDF(file);
          console.log(`📝 Texto extraído do PDF: ${text.length} caracteres`);
        } catch (pdfError) {
          console.warn('⚠️ Erro ao processar PDF, usando texto simulado:', pdfError);
          text = this.generateFallbackText(file.name);
        }
      } else if (file.type.startsWith('text/')) {
        // Arquivos de texto
        text = await file.text();
        console.log(`📝 Texto extraído: ${text.length} caracteres`);
      } else {
        // Outros tipos - usar texto simulado
        console.warn('⚠️ Tipo de arquivo não suportado, usando texto simulado');
        text = this.generateFallbackText(file.name);
      }

      // 2. Dividir em chunks
      const chunks = documentProcessor.chunkText(text, {
        chunkSize: 1000,
        overlap: 200
      });
      console.log(`📦 Criados ${chunks.length} chunks`);

      // 3. Gerar embeddings para cada chunk
      const chunksWithEmbeddings = [];
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        console.log(`🧮 Gerando embedding ${i + 1}/${chunks.length}`);

        try {
          const embedding = await embeddingService.generateEmbedding(chunk);

          chunksWithEmbeddings.push({
            document_id: document.id,
            content: chunk,
            chunk_index: i,
            embedding: embedding.embedding,
            metadata: {
              page_number: Math.floor(i / 3) + 1, // Estimativa
              section: `Seção ${i + 1}`,
              file_type: file.type
            }
          });
        } catch (embeddingError) {
          console.warn(`⚠️ Erro ao gerar embedding para chunk ${i + 1}:`, embeddingError);
          // Continuar com os outros chunks
        }

        // Pequena pausa para não sobrecarregar a API
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      if (chunksWithEmbeddings.length === 0) {
        throw new Error('Nenhum chunk foi processado com sucesso');
      }

      // 4. Salvar chunks no banco
      await databaseService.saveDocumentChunks(chunksWithEmbeddings);
      console.log(`💾 ${chunksWithEmbeddings.length} chunks salvos no banco`);

      // 5. Atualizar status do documento
      await databaseService.updateDocumentStatus(document.id, 'ready', chunksWithEmbeddings.length);
      console.log('✅ Documento processado com sucesso');

    } catch (error) {
      console.error('❌ Erro no processamento do documento:', error);
      await databaseService.updateDocumentStatus(document.id, 'error');
      throw error; // Re-throw para que o erro seja visível
    }
  }

  private generateFallbackText(fileName: string): string {
    return `
# Documento: ${fileName}

Este é um documento de exemplo que foi carregado no sistema da Vereadora Rafaela de Nilda.

## Informações Gerais sobre Parnamirim/RN

Parnamirim é um município do Rio Grande do Norte, localizado na região metropolitana de Natal.
A cidade possui aproximadamente 267.000 habitantes e é conhecida por suas praias e pelo
desenvolvimento urbano planejado.

## Serviços Municipais

### Saúde
- Unidades Básicas de Saúde (UBS) distribuídas pelos bairros
- Hospital Municipal Dr. Deoclécio Marques de Lucena
- Unidade de Pronto Atendimento (UPA)
- Centro de Especialidades Odontológicas (CEO)

### Educação
- Escolas municipais de ensino fundamental
- Creches municipais
- Programas de educação de jovens e adultos
- Transporte escolar

### Infraestrutura
- Pavimentação de vias públicas
- Sistema de iluminação pública
- Rede de saneamento básico
- Coleta de lixo e limpeza urbana

## Atuação da Vereadora Rafaela de Nilda

A Vereadora Rafaela de Nilda atua em diversas frentes para melhorar a qualidade de vida
dos cidadãos de Parnamirim, incluindo:

- Fiscalização dos serviços públicos
- Proposição de projetos de lei
- Atendimento direto à população
- Articulação com outros poderes
- Transparência na gestão pública

Para mais informações ou para solicitar atendimento, entre em contato através dos
canais oficiais da vereadora.
    `.trim();
  }

  private buildPrompt(question: string, context: string): string {
    return `${SYSTEM_PROMPT}

CONTEXTO DISPONÍVEL:
${context || 'Nenhum documento específico encontrado.'}

PERGUNTA DO CIDADÃO:
${question}

INSTRUÇÕES PARA RESPOSTA:
1. 🎯 IDENTIDADE: Você é a Vereadora Rafaela de Nilda, representante do povo de Parnamirim/RN
2. 📋 CONTEXTO: Use SEMPRE o contexto fornecido para responder às perguntas
3. 🏛️ FOCO: Mantenha-se estritamente em questões municipais de Parnamirim/RN
4. ✅ HONESTIDADE: Se o contexto não contém informações suficientes, seja transparente
5. 🎯 CLAREZA: Seja clara, objetiva e útil em suas respostas
6. 🔗 ENCAMINHAMENTOS: Ofereça sempre encaminhamentos práticos quando apropriado
7. 🤝 DISPONIBILIDADE: Termine sempre oferecendo ajuda adicional
8. 📱 CONTATO: Mencione seus canais de atendimento quando relevante
9. 🏃‍♀️ AÇÃO: Demonstre proatividade e compromisso com soluções
10. ❤️ EMPATIA: Mostre compreensão e proximidade com os cidadãos

ESTILO DE COMUNICAÇÃO:
- Use linguagem acessível e próxima ao povo
- Demonstre conhecimento técnico quando necessário
- Seja empática e acolhedora
- Mostre determinação e compromisso
- Use emojis moderadamente para humanizar a comunicação

RESPOSTA:`;
  }

  private calculateConfidence(chunksFound: number, answer: string): number {
    let confidence = 0.5; // Base

    // Aumentar confiança baseado em chunks encontrados
    if (chunksFound > 0) confidence += 0.2;
    if (chunksFound > 2) confidence += 0.1;
    if (chunksFound > 4) confidence += 0.1;

    // Aumentar confiança baseado no tamanho da resposta
    if (answer.length > 100) confidence += 0.1;
    if (answer.length > 300) confidence += 0.1;

    // Diminuir confiança se resposta contém palavras de incerteza
    const uncertaintyWords = ['não sei', 'não tenho', 'desculpe', 'talvez', 'possivelmente'];
    const hasUncertainty = uncertaintyWords.some(word => 
      answer.toLowerCase().includes(word)
    );
    
    if (hasUncertainty) confidence -= 0.2;

    return Math.max(0, Math.min(1, confidence));
  }

  // Gerar hash do contexto para cache de respostas
  private async generateContextHash(userMessage: string): Promise<string> {
    try {
      // Buscar chunks disponíveis para criar hash do contexto atual
      const availableChunks = await databaseService.getDocumentChunks();
      const contextInfo = {
        totalChunks: availableChunks.length,
        lastUpdate: Math.max(...availableChunks.map((c: any) =>
          new Date(c.metadata?.created_at || Date.now()).getTime()
        ), 0),
        documentIds: [...new Set(availableChunks.map((c: any) => c.document_id))].sort(),
        queryLength: userMessage.length
      };

      return this.hashString(JSON.stringify(contextInfo));
    } catch (error) {
      // Fallback para hash simples se houver erro
      return this.hashString(userMessage + Date.now().toString());
    }
  }

  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  // Método de fallback quando APIs não estão configuradas
  private generateFallbackResponse(userMessage: string, startTime: number): RAGResponse {
    const responseTime = Date.now() - startTime;

    // Respostas pré-definidas baseadas em palavras-chave
    const responses = this.getFallbackResponses();
    const messageKey = userMessage.toLowerCase();

    let answer = responses.default;

    // Buscar resposta baseada em palavras-chave
    for (const [keywords, response] of Object.entries(responses.keywords)) {
      if (keywords.split(',').some(keyword => messageKey.includes(keyword.trim()))) {
        answer = response;
        break;
      }
    }

    return {
      answer,
      sources: [],
      confidence: 0.6,
      processing_time: responseTime
    };
  }

  private getFallbackResponses() {
    return {
      default: `Olá! Sou o assistente virtual da Vereadora Rafaela de Nilda.

🏛️ **Sobre o Gabinete:**
Estou aqui para ajudar com informações sobre o mandato da Vereadora Rafaela de Nilda na Câmara Municipal de Parnamirim/RN.

⚠️ **Modo Demonstração:**
No momento, estou funcionando em modo demonstração. Para acesso completo às funcionalidades e documentos oficiais, é necessário configurar as integrações com os serviços externos.

📞 **Contato Direto:**
Para questões específicas ou urgentes, entre em contato diretamente com o gabinete:
- 📧 Email: <EMAIL>
- 📱 WhatsApp: (84) 99999-9999

Como posso ajudá-lo hoje?`,

      keywords: {
        'projeto,lei,legislação': `📋 **Projetos de Lei**

A Vereadora Rafaela de Nilda trabalha constantemente na elaboração e análise de projetos de lei que beneficiem os cidadãos de Parnamirim.

🔍 **Para consultar projetos específicos:**
- Acesse o site da Câmara Municipal de Parnamirim
- Entre em contato com o gabinete para informações detalhadas

📞 **Contato:** (84) 99999-9999`,

        'horário,atendimento,funcionamento': `🕐 **Horário de Atendimento**

**Gabinete da Vereadora Rafaela de Nilda:**
- Segunda a Sexta: 8h às 17h
- Local: Câmara Municipal de Parnamirim/RN

📞 **Agendamentos:**
- Telefone: (84) 99999-9999
- Email: <EMAIL>

💡 **Dica:** Recomendamos agendar previamente para garantir o atendimento personalizado.`,

        'serviço,público,parnamirim': `🏛️ **Serviços Públicos em Parnamirim**

A Vereadora Rafaela de Nilda atua na fiscalização e melhoria dos serviços públicos municipais:

🚌 **Transporte Público**
🏥 **Saúde Municipal**
📚 **Educação**
🛣️ **Infraestrutura**
🌳 **Meio Ambiente**

📞 **Para denúncias ou sugestões:**
Entre em contato com o gabinete: (84) 99999-9999`,

        'transparência,prestação,contas': `📊 **Transparência e Prestação de Contas**

A Vereadora Rafaela de Nilda preza pela transparência total em seu mandato:

🔍 **Portal da Transparência:**
- Gastos do gabinete
- Projetos apresentados
- Votações e posicionamentos

📱 **Redes Sociais:**
Acompanhe as atividades diárias e relatórios de trabalho.

📞 **Contato:** (84) 99999-9999 para esclarecimentos adicionais.`
      }
    };
  }

  // Método para busca direta (sem RAG)
  async simpleQuery(question: string): Promise<string> {
    try {
      if (!isGeminiConfigured || !this.model) {
        const fallbackResponse = this.generateFallbackResponse(question, Date.now());
        return fallbackResponse.answer;
      }

      const prompt = `${SYSTEM_PROMPT}

PERGUNTA: ${question}

Responda como assistente da Vereadora Rafaela de Nilda, mesmo sem documentos específicos.`;

      const result = await this.model.generateContent(prompt);
      return result.response.text();
    } catch (error) {
      console.error('Erro na consulta simples:', error);
      return 'Desculpe, não foi possível processar sua pergunta no momento.';
    }
  }
}

export const vereadoraRAGService = new VereadoraRAGService();
