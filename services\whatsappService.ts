import axios from 'axios';
import React from 'react';

// URL do backend WhatsApp
const WHATSAPP_BACKEND_URL = import.meta.env.VITE_WHATSAPP_BACKEND_URL || 'http://localhost:3001';

// Interface para status da conexão WhatsApp
export interface WhatsAppStatus {
  isConnected: boolean;
  sessionName: string;
  qrCode?: {
    base64: string;
    ascii: string;
    path: string;
    timestamp: string;
    attempt?: number;
  };
  hasQRCode?: boolean;
  qrCodeUrl?: string | null;
  connectionAttempts: number;
  maxAttempts?: number;
  status?: string;
  statusCode?: string;
  timestamp: string;
}

// Interface para estatísticas do WhatsApp
export interface WhatsAppStats {
  connection: WhatsAppStatus;
  messages: {
    activeSessions: number;
    totalMessages: number;
    rateLimitedUsers: number;
    autoReplyEnabled: boolean;
    businessHoursOnly: boolean;
    isBusinessHours: boolean;
  };
  uptime: number;
  timestamp: string;
  antiBan?: {
    riskLevel: number;
    isInBreak: boolean;
    breakTimeRemaining: number;
    humanBehaviorScore: number;
    isHumanActivityTime: boolean;
    safeToSend: boolean;
    sessionTime: number;
    maxSessionTime: number;
    suspiciousActivities: number;
    banWarnings: number;
  };
}

// Interface para envio de mensagem
export interface SendMessageRequest {
  to: string;
  message: string;
  type?: 'text' | 'image' | 'document';
  imagePath?: string;
  documentPath?: string;
  filename?: string;
  caption?: string;
}

// Interface para resposta de envio
export interface SendMessageResponse {
  success: boolean;
  data?: {
    messageId: string;
    to: string;
    type: string;
    status: string;
    timestamp: string;
  };
  error?: string;
  message?: string;
}

class WhatsAppService {
  private baseURL: string;
  private httpClient: any;

  constructor() {
    this.baseURL = WHATSAPP_BACKEND_URL;
    
    // Configurar cliente HTTP
    this.httpClient = axios.create({
      baseURL: `${this.baseURL}/api`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      }
    });

    // Interceptors para logging
    this.httpClient.interceptors.request.use(
      (config: any) => {
        console.log(`🔄 WhatsApp API: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error: any) => {
        console.error('❌ WhatsApp API Request Error:', error);
        return Promise.reject(error);
      }
    );

    this.httpClient.interceptors.response.use(
      (response: any) => {
        console.log(`✅ WhatsApp API: ${response.status} ${response.config.url}`);
        return response;
      },
      (error: any) => {
        console.error('❌ WhatsApp API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Verificar se o backend está disponível
  async isBackendAvailable(): Promise<boolean> {
    try {
      const response = await this.httpClient.get('/health');
      return response.status === 200;
    } catch (error) {
      console.warn('⚠️ Backend WhatsApp não disponível:', error);
      return false;
    }
  }

  // Obter status da conexão WhatsApp
  async getStatus(): Promise<WhatsAppStatus | null> {
    try {
      const response = await this.httpClient.get('/whatsapp/status');
      return response.data.data;
    } catch (error) {
      console.error('❌ Erro ao obter status WhatsApp:', error);
      return null;
    }
  }

  // Obter QR Code atual
  async getQRCode(): Promise<WhatsAppStatus['qrCode'] | null> {
    try {
      const response = await this.httpClient.get('/whatsapp/qr');
      return response.data.data.qrCode;
    } catch (error) {
      console.error('❌ Erro ao obter QR Code:', error);
      return null;
    }
  }

  // Obter estatísticas completas
  async getStats(): Promise<WhatsAppStats | null> {
    try {
      const response = await this.httpClient.get('/whatsapp/stats');
      return response.data.data;
    } catch (error) {
      console.error('❌ Erro ao obter estatísticas WhatsApp:', error);
      return null;
    }
  }

  // Enviar mensagem
  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    try {
      const response = await this.httpClient.post('/whatsapp/send', request);
      return {
        success: true,
        data: response.data.data,
        message: response.data.message
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        message: 'Erro ao enviar mensagem'
      };
    }
  }

  // Reiniciar conexão WhatsApp
  async restart(): Promise<boolean> {
    try {
      const response = await this.httpClient.post('/whatsapp/restart');
      return response.data.success;
    } catch (error) {
      console.error('❌ Erro ao reiniciar WhatsApp:', error);
      return false;
    }
  }

  // Obter lista de chats
  async getChats(): Promise<any[]> {
    try {
      const response = await this.httpClient.get('/whatsapp/chats');
      return response.data.data.chats || [];
    } catch (error) {
      console.error('❌ Erro ao obter chats:', error);
      return [];
    }
  }



  // Verificar se está configurado
  isConfigured(): boolean {
    return !!import.meta.env.VITE_WHATSAPP_BACKEND_URL || true; // Default para localhost
  }

  // Obter URL do backend
  getBackendURL(): string {
    return this.baseURL;
  }

  // Polling para atualizações de status
  startStatusPolling(callback: (status: WhatsAppStatus | null) => void, interval: number = 5000): () => void {
    const intervalId = setInterval(async () => {
      const status = await this.getStatus();
      callback(status);
    }, interval);

    // Retornar função para parar o polling
    return () => clearInterval(intervalId);
  }

  // WebSocket para atualizações em tempo real (se implementado no backend)
  connectWebSocket(onMessage: (data: any) => void): WebSocket | null {
    try {
      const wsUrl = this.baseURL.replace('http', 'ws') + '/ws';
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log('🔌 WebSocket WhatsApp conectado');
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          onMessage(data);
        } catch (error) {
          console.error('❌ Erro ao processar mensagem WebSocket:', error);
        }
      };

      ws.onclose = () => {
        console.log('🔌 WebSocket WhatsApp desconectado');
      };

      ws.onerror = (error) => {
        console.error('❌ Erro WebSocket WhatsApp:', error);
      };

      return ws;
    } catch (error) {
      console.error('❌ Erro ao conectar WebSocket:', error);
      return null;
    }
  }

  // Métodos para conversas sincronizadas
  async getChats(): Promise<any> {
    const response = await fetch(`${this.apiBase}/whatsapp/chats`);
    if (!response.ok) {
      throw new Error('Falha ao obter conversas');
    }
    return response.json();
  }

  async getChatMessages(chatId: string, limit: number = 50): Promise<any> {
    const response = await fetch(`${this.apiBase}/whatsapp/chats/${encodeURIComponent(chatId)}/messages?limit=${limit}`);
    if (!response.ok) {
      throw new Error('Falha ao obter mensagens');
    }
    return response.json();
  }

  async startSync(): Promise<any> {
    const response = await fetch(`${this.apiBase}/whatsapp/sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Falha ao iniciar sincronização');
    }

    return response.json();
  }

  async getStats(): Promise<any> {
    const response = await fetch(`${this.apiBase}/whatsapp/stats`);
    if (!response.ok) {
      throw new Error('Falha ao obter estatísticas');
    }
    return response.json();
  }
}

// Instância singleton
export const whatsappService = new WhatsAppService();

// Hook personalizado para React
export const useWhatsAppStatus = (autoRefresh: boolean = true) => {
  const [status, setStatus] = React.useState<WhatsAppStatus | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    let stopPolling: (() => void) | null = null;

    const loadStatus = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const isAvailable = await whatsappService.isBackendAvailable();
        if (!isAvailable) {
          setError('Backend WhatsApp não disponível');
          return;
        }

        const currentStatus = await whatsappService.getStatus();
        setStatus(currentStatus);

        if (autoRefresh && currentStatus) {
          stopPolling = whatsappService.startStatusPolling(setStatus, 5000);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    };

    loadStatus();

    return () => {
      if (stopPolling) {
        stopPolling();
      }
    };
  }, [autoRefresh]);

  const refresh = () => {
    loadStatus();
  };

  return { status, loading, error, refresh };
};

export default whatsappService;
